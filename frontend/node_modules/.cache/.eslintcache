[{"/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/index.js": "1", "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/App.js": "2", "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/hooks/useMicrophone.js": "3", "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/hooks/useVoiceSocket.js": "4", "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/components/CallControls.jsx": "5", "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/components/CallStatus.jsx": "6", "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/utils/audioProcessor.js": "7"}, {"size": 254, "mtime": 1753606718566, "results": "8", "hashOfConfig": "9"}, {"size": 11845, "mtime": 1753629663264, "results": "10", "hashOfConfig": "9"}, {"size": 8542, "mtime": 1753612516253, "results": "11", "hashOfConfig": "9"}, {"size": 14194, "mtime": 1753630253165, "results": "12", "hashOfConfig": "9"}, {"size": 9972, "mtime": 1753630370990, "results": "13", "hashOfConfig": "9"}, {"size": 10697, "mtime": 1753606915721, "results": "14", "hashOfConfig": "9"}, {"size": 13240, "mtime": 1753630870882, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1bjvwel", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/index.js", [], [], "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/App.js", ["37", "38"], [], "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/hooks/useMicrophone.js", [], [], "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/hooks/useVoiceSocket.js", [], [], "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/components/CallControls.jsx", [], [], "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/components/CallStatus.jsx", ["39", "40"], [], "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/utils/audioProcessor.js", [], [], {"ruleId": "41", "severity": 1, "message": "42", "line": 45, "column": 19, "nodeType": "43", "messageId": "44", "endLine": 45, "endColumn": 34}, {"ruleId": "45", "severity": 1, "message": "46", "line": 61, "column": 6, "nodeType": "47", "endLine": 61, "endColumn": 19, "suggestions": "48"}, {"ruleId": "41", "severity": 1, "message": "49", "line": 7, "column": 3, "nodeType": "43", "messageId": "44", "endLine": 7, "endColumn": 8}, {"ruleId": "41", "severity": 1, "message": "50", "line": 9, "column": 3, "nodeType": "43", "messageId": "44", "endLine": 9, "endColumn": 14}, "no-unused-vars", "'isMicRequesting' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'connect'. Either include it or remove the dependency array.", "ArrayExpression", ["51"], "'Clock' is defined but never used.", "'CheckCircle' is defined but never used.", {"desc": "52", "fix": "53"}, "Update the dependencies array to be: [autoConnect, connect]", {"range": "54", "text": "55"}, [1599, 1612], "[autoConnect, connect]"]