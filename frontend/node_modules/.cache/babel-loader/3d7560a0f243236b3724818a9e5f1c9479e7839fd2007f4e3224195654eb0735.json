{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/components/CallControls.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Phone, PhoneOff, Mic, MicOff, Volume2, VolumeX, Loader2, TestTube } from 'lucide-react';\nimport { CALL_STATES } from '../hooks/useVoiceSocket';\nimport { audioProcessor } from '../utils/audioProcessor';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CallControls = ({\n  callState,\n  isConnected,\n  isMuted,\n  onStartCall,\n  onEndCall,\n  onToggleMute,\n  audioLevel = 0\n}) => {\n  _s();\n  const [speakerEnabled, setSpeakerEnabled] = useState(true);\n  const isInCall = callState === CALL_STATES.IN_CALL;\n  const isConnecting = callState === CALL_STATES.CONNECTING;\n  const isEnding = callState === CALL_STATES.ENDING;\n  const canStartCall = isConnected && callState === CALL_STATES.IDLE;\n  const canEndCall = isInCall || isConnecting;\n  const canUseControls = isInCall || isConnecting; // Enable controls when call is active\n\n  const handleStartCall = () => {\n    if (canStartCall) {\n      onStartCall();\n    }\n  };\n  const handleEndCall = () => {\n    if (canEndCall) {\n      onEndCall();\n    }\n  };\n  const handleToggleMute = () => {\n    if (canUseControls) {\n      onToggleMute();\n    }\n  };\n  const handleToggleSpeaker = () => {\n    setSpeakerEnabled(prev => !prev);\n    // In a real implementation, you would control audio output routing here\n  };\n  const handleTestAudio = async () => {\n    try {\n      // Create a simple AudioContext for testing\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      if (audioContext.state === 'suspended') {\n        await audioContext.resume();\n      }\n\n      // Create a simple 440Hz tone for 0.5 seconds\n      const sampleRate = audioContext.sampleRate;\n      const duration = 0.5;\n      const frequency = 440;\n      const length = sampleRate * duration;\n      const audioBuffer = audioContext.createBuffer(1, length, sampleRate);\n      const channelData = audioBuffer.getChannelData(0);\n      for (let i = 0; i < length; i++) {\n        channelData[i] = Math.sin(2 * Math.PI * frequency * i / sampleRate) * 0.3;\n      }\n      const source = audioContext.createBufferSource();\n      source.buffer = audioBuffer;\n      source.connect(audioContext.destination);\n      source.start();\n      console.log('Test audio played - 440Hz tone for 0.5 seconds');\n    } catch (error) {\n      console.error('Error testing audio:', error);\n    }\n  };\n\n  // Audio level visualization\n  const AudioLevelIndicator = () => {\n    if (!isInCall || isMuted) return null;\n    const bars = 5;\n    const activeBarCount = Math.ceil(audioLevel / 100 * bars);\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-1 ml-2\",\n      children: Array.from({\n        length: bars\n      }, (_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `w-1 h-4 rounded-full transition-colors duration-150 ${i < activeBarCount ? i < 2 ? 'bg-success-500' : i < 4 ? 'bg-yellow-500' : 'bg-danger-500' : 'bg-gray-300'}`\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-semibold text-gray-900 mb-2\",\n        children: \"Call Controls\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-600\",\n        children: [!isConnected && 'Connect to server to start calling', isConnected && callState === CALL_STATES.IDLE && 'Ready to start call', isConnecting && 'Connecting to call...', isInCall && 'Call in progress', isEnding && 'Ending call...', callState === CALL_STATES.ENDED && 'Call ended', callState === CALL_STATES.ERROR && 'Call error occurred']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center mb-6\",\n      children: !isInCall && !isConnecting && !isEnding ? /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleStartCall,\n        disabled: !canStartCall,\n        className: `\n              relative w-16 h-16 rounded-full flex items-center justify-center\n              transition-all duration-200 transform hover:scale-105 focus:outline-none\n              focus:ring-4 focus:ring-offset-2 shadow-lg\n              ${canStartCall ? 'bg-success-600 hover:bg-success-700 focus:ring-success-500 text-white' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}\n            `,\n        children: [/*#__PURE__*/_jsxDEV(Phone, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 13\n        }, this), canStartCall && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 rounded-full bg-success-600 animate-ping opacity-20\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleEndCall,\n        disabled: !canEndCall,\n        className: `\n              relative w-16 h-16 rounded-full flex items-center justify-center\n              transition-all duration-200 transform hover:scale-105 focus:outline-none\n              focus:ring-4 focus:ring-offset-2 shadow-lg\n              ${canEndCall ? 'bg-danger-600 hover:bg-danger-700 focus:ring-danger-500 text-white' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}\n            `,\n        children: [isEnding ? /*#__PURE__*/_jsxDEV(Loader2, {\n          size: 24,\n          className: \"animate-spin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(PhoneOff, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 15\n        }, this), isInCall && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 rounded-full bg-danger-600 animate-pulse opacity-30\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center space-x-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleToggleMute,\n          disabled: !canUseControls,\n          className: `\n              w-12 h-12 rounded-full flex items-center justify-center\n              transition-all duration-200 transform hover:scale-105 focus:outline-none\n              focus:ring-2 focus:ring-offset-2 shadow-md\n              ${!canUseControls ? 'bg-gray-200 text-gray-400 cursor-not-allowed' : isMuted ? 'bg-danger-100 text-danger-600 hover:bg-danger-200 focus:ring-danger-500' : 'bg-gray-100 text-gray-600 hover:bg-gray-200 focus:ring-gray-500'}\n            `,\n          children: isMuted ? /*#__PURE__*/_jsxDEV(MicOff, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 24\n          }, this) : /*#__PURE__*/_jsxDEV(Mic, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mt-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-500\",\n            children: isMuted ? 'Muted' : 'Mic'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(AudioLevelIndicator, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleToggleSpeaker,\n          disabled: !canUseControls,\n          className: `\n              w-12 h-12 rounded-full flex items-center justify-center\n              transition-all duration-200 transform hover:scale-105 focus:outline-none\n              focus:ring-2 focus:ring-offset-2 shadow-md\n              ${!canUseControls ? 'bg-gray-200 text-gray-400 cursor-not-allowed' : !speakerEnabled ? 'bg-danger-100 text-danger-600 hover:bg-danger-200 focus:ring-danger-500' : 'bg-gray-100 text-gray-600 hover:bg-gray-200 focus:ring-gray-500'}\n            `,\n          children: speakerEnabled ? /*#__PURE__*/_jsxDEV(Volume2, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 31\n          }, this) : /*#__PURE__*/_jsxDEV(VolumeX, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 55\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs text-gray-500 mt-1\",\n          children: speakerEnabled ? 'Speaker' : 'Muted'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleTestAudio,\n          className: \"w-12 h-12 rounded-full flex items-center justify-center transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 shadow-md bg-blue-100 text-blue-600 hover:bg-blue-200 focus:ring-blue-500\",\n          children: /*#__PURE__*/_jsxDEV(TestTube, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs text-gray-500 mt-1\",\n          children: \"Test\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this), isConnecting && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 flex items-center justify-center space-x-2\",\n      children: [/*#__PURE__*/_jsxDEV(Loader2, {\n        size: 16,\n        className: \"animate-spin text-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-sm text-primary-600\",\n        children: \"Connecting to call...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 9\n    }, this), isInCall && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"inline-flex items-center space-x-2 px-3 py-1 bg-success-100 text-success-800 rounded-full text-sm\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-2 h-2 bg-success-500 rounded-full animate-pulse\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Call Active\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 9\n    }, this), callState === CALL_STATES.ERROR && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"inline-flex items-center space-x-2 px-3 py-1 bg-danger-100 text-danger-800 rounded-full text-sm\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-2 h-2 bg-danger-500 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Call Error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-xs text-gray-500 space-y-1\",\n        children: [!isConnected && /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Please connect to the server first\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 13\n        }, this), isConnected && callState === CALL_STATES.IDLE && /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Click the green phone button to start a call\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 13\n        }, this), isInCall && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Use the microphone button to mute/unmute\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Click the red phone button to end the call\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 117,\n    columnNumber: 5\n  }, this);\n};\n_s(CallControls, \"TwGL6T/geUdOy0ow2aEFHNtpaaI=\");\n_c = CallControls;\nexport default CallControls;\nvar _c;\n$RefreshReg$(_c, \"CallControls\");", "map": {"version": 3, "names": ["React", "useState", "Phone", "PhoneOff", "Mic", "<PERSON><PERSON><PERSON><PERSON>", "Volume2", "VolumeX", "Loader2", "TestTube", "CALL_STATES", "audioProcessor", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CallControls", "callState", "isConnected", "isMuted", "onStartCall", "onEndCall", "onToggleMute", "audioLevel", "_s", "speakerEnabled", "setSpeakerEnabled", "isInCall", "IN_CALL", "isConnecting", "CONNECTING", "isEnding", "ENDING", "canStartCall", "IDLE", "canEndCall", "canUseControls", "handleStartCall", "handleEndCall", "handleToggleMute", "handleToggleSpeaker", "prev", "handleTestAudio", "audioContext", "window", "AudioContext", "webkitAudioContext", "state", "resume", "sampleRate", "duration", "frequency", "length", "audioBuffer", "createBuffer", "channelData", "getChannelData", "i", "Math", "sin", "PI", "source", "createBufferSource", "buffer", "connect", "destination", "start", "console", "log", "error", "AudioLevelIndicator", "bars", "activeBarCount", "ceil", "className", "children", "Array", "from", "_", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ENDED", "ERROR", "onClick", "disabled", "size", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/components/CallControls.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Phone,\n  PhoneOff,\n  Mic,\n  MicOff,\n  Volume2,\n  VolumeX,\n  Loader2,\n  TestTube\n} from 'lucide-react';\nimport { CALL_STATES } from '../hooks/useVoiceSocket';\nimport { audioProcessor } from '../utils/audioProcessor';\n\nconst CallControls = ({ \n  callState, \n  isConnected, \n  isMuted, \n  onStartCall, \n  onEndCall, \n  onToggleMute,\n  audioLevel = 0 \n}) => {\n  const [speakerEnabled, setSpeakerEnabled] = useState(true);\n\n  const isInCall = callState === CALL_STATES.IN_CALL;\n  const isConnecting = callState === CALL_STATES.CONNECTING;\n  const isEnding = callState === CALL_STATES.ENDING;\n  const canStartCall = isConnected && callState === CALL_STATES.IDLE;\n  const canEndCall = isInCall || isConnecting;\n  const canUseControls = isInCall || isConnecting; // Enable controls when call is active\n\n  const handleStartCall = () => {\n    if (canStartCall) {\n      onStartCall();\n    }\n  };\n\n  const handleEndCall = () => {\n    if (canEndCall) {\n      onEndCall();\n    }\n  };\n\n  const handleToggleMute = () => {\n    if (canUseControls) {\n      onToggleMute();\n    }\n  };\n\n  const handleToggleSpeaker = () => {\n    setSpeakerEnabled(prev => !prev);\n    // In a real implementation, you would control audio output routing here\n  };\n\n  const handleTestAudio = async () => {\n    try {\n      // Create a simple AudioContext for testing\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n      if (audioContext.state === 'suspended') {\n        await audioContext.resume();\n      }\n\n      // Create a simple 440Hz tone for 0.5 seconds\n      const sampleRate = audioContext.sampleRate;\n      const duration = 0.5;\n      const frequency = 440;\n      const length = sampleRate * duration;\n\n      const audioBuffer = audioContext.createBuffer(1, length, sampleRate);\n      const channelData = audioBuffer.getChannelData(0);\n\n      for (let i = 0; i < length; i++) {\n        channelData[i] = Math.sin(2 * Math.PI * frequency * i / sampleRate) * 0.3;\n      }\n\n      const source = audioContext.createBufferSource();\n      source.buffer = audioBuffer;\n      source.connect(audioContext.destination);\n      source.start();\n\n      console.log('Test audio played - 440Hz tone for 0.5 seconds');\n    } catch (error) {\n      console.error('Error testing audio:', error);\n    }\n  };\n\n  // Audio level visualization\n  const AudioLevelIndicator = () => {\n    if (!isInCall || isMuted) return null;\n\n    const bars = 5;\n    const activeBarCount = Math.ceil((audioLevel / 100) * bars);\n\n    return (\n      <div className=\"flex items-center space-x-1 ml-2\">\n        {Array.from({ length: bars }, (_, i) => (\n          <div\n            key={i}\n            className={`w-1 h-4 rounded-full transition-colors duration-150 ${\n              i < activeBarCount \n                ? i < 2 \n                  ? 'bg-success-500' \n                  : i < 4 \n                    ? 'bg-yellow-500' \n                    : 'bg-danger-500'\n                : 'bg-gray-300'\n            }`}\n          />\n        ))}\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"card p-6\">\n      <div className=\"text-center mb-6\">\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">Call Controls</h2>\n        <p className=\"text-sm text-gray-600\">\n          {!isConnected && 'Connect to server to start calling'}\n          {isConnected && callState === CALL_STATES.IDLE && 'Ready to start call'}\n          {isConnecting && 'Connecting to call...'}\n          {isInCall && 'Call in progress'}\n          {isEnding && 'Ending call...'}\n          {callState === CALL_STATES.ENDED && 'Call ended'}\n          {callState === CALL_STATES.ERROR && 'Call error occurred'}\n        </p>\n      </div>\n\n      {/* Main Call Button */}\n      <div className=\"flex justify-center mb-6\">\n        {!isInCall && !isConnecting && !isEnding ? (\n          <button\n            onClick={handleStartCall}\n            disabled={!canStartCall}\n            className={`\n              relative w-16 h-16 rounded-full flex items-center justify-center\n              transition-all duration-200 transform hover:scale-105 focus:outline-none\n              focus:ring-4 focus:ring-offset-2 shadow-lg\n              ${canStartCall \n                ? 'bg-success-600 hover:bg-success-700 focus:ring-success-500 text-white' \n                : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n              }\n            `}\n          >\n            <Phone size={24} />\n            {canStartCall && (\n              <div className=\"absolute inset-0 rounded-full bg-success-600 animate-ping opacity-20\"></div>\n            )}\n          </button>\n        ) : (\n          <button\n            onClick={handleEndCall}\n            disabled={!canEndCall}\n            className={`\n              relative w-16 h-16 rounded-full flex items-center justify-center\n              transition-all duration-200 transform hover:scale-105 focus:outline-none\n              focus:ring-4 focus:ring-offset-2 shadow-lg\n              ${canEndCall \n                ? 'bg-danger-600 hover:bg-danger-700 focus:ring-danger-500 text-white' \n                : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n              }\n            `}\n          >\n            {isEnding ? (\n              <Loader2 size={24} className=\"animate-spin\" />\n            ) : (\n              <PhoneOff size={24} />\n            )}\n            {isInCall && (\n              <div className=\"absolute inset-0 rounded-full bg-danger-600 animate-pulse opacity-30\"></div>\n            )}\n          </button>\n        )}\n      </div>\n\n      {/* Secondary Controls */}\n      <div className=\"flex justify-center space-x-4\">\n        {/* Microphone Control */}\n        <div className=\"flex flex-col items-center\">\n          <button\n            onClick={handleToggleMute}\n            disabled={!canUseControls}\n            className={`\n              w-12 h-12 rounded-full flex items-center justify-center\n              transition-all duration-200 transform hover:scale-105 focus:outline-none\n              focus:ring-2 focus:ring-offset-2 shadow-md\n              ${!canUseControls\n                ? 'bg-gray-200 text-gray-400 cursor-not-allowed'\n                : isMuted\n                  ? 'bg-danger-100 text-danger-600 hover:bg-danger-200 focus:ring-danger-500'\n                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200 focus:ring-gray-500'\n              }\n            `}\n          >\n            {isMuted ? <MicOff size={20} /> : <Mic size={20} />}\n          </button>\n          <div className=\"flex items-center mt-1\">\n            <span className=\"text-xs text-gray-500\">\n              {isMuted ? 'Muted' : 'Mic'}\n            </span>\n            <AudioLevelIndicator />\n          </div>\n        </div>\n\n        {/* Speaker Control */}\n        <div className=\"flex flex-col items-center\">\n          <button\n            onClick={handleToggleSpeaker}\n            disabled={!canUseControls}\n            className={`\n              w-12 h-12 rounded-full flex items-center justify-center\n              transition-all duration-200 transform hover:scale-105 focus:outline-none\n              focus:ring-2 focus:ring-offset-2 shadow-md\n              ${!canUseControls\n                ? 'bg-gray-200 text-gray-400 cursor-not-allowed'\n                : !speakerEnabled\n                  ? 'bg-danger-100 text-danger-600 hover:bg-danger-200 focus:ring-danger-500'\n                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200 focus:ring-gray-500'\n              }\n            `}\n          >\n            {speakerEnabled ? <Volume2 size={20} /> : <VolumeX size={20} />}\n          </button>\n          <span className=\"text-xs text-gray-500 mt-1\">\n            {speakerEnabled ? 'Speaker' : 'Muted'}\n          </span>\n        </div>\n\n        {/* Audio Test Control */}\n        <div className=\"flex flex-col items-center\">\n          <button\n            onClick={handleTestAudio}\n            className=\"w-12 h-12 rounded-full flex items-center justify-center\n              transition-all duration-200 transform hover:scale-105 focus:outline-none\n              focus:ring-2 focus:ring-offset-2 shadow-md\n              bg-blue-100 text-blue-600 hover:bg-blue-200 focus:ring-blue-500\"\n          >\n            <TestTube size={20} />\n          </button>\n          <span className=\"text-xs text-gray-500 mt-1\">Test</span>\n        </div>\n      </div>\n\n      {/* Call Status Indicators */}\n      {isConnecting && (\n        <div className=\"mt-6 flex items-center justify-center space-x-2\">\n          <Loader2 size={16} className=\"animate-spin text-primary-600\" />\n          <span className=\"text-sm text-primary-600\">Connecting to call...</span>\n        </div>\n      )}\n\n      {isInCall && (\n        <div className=\"mt-6 text-center\">\n          <div className=\"inline-flex items-center space-x-2 px-3 py-1 bg-success-100 text-success-800 rounded-full text-sm\">\n            <div className=\"w-2 h-2 bg-success-500 rounded-full animate-pulse\"></div>\n            <span>Call Active</span>\n          </div>\n        </div>\n      )}\n\n      {callState === CALL_STATES.ERROR && (\n        <div className=\"mt-6 text-center\">\n          <div className=\"inline-flex items-center space-x-2 px-3 py-1 bg-danger-100 text-danger-800 rounded-full text-sm\">\n            <div className=\"w-2 h-2 bg-danger-500 rounded-full\"></div>\n            <span>Call Error</span>\n          </div>\n        </div>\n      )}\n\n      {/* Instructions */}\n      <div className=\"mt-6 text-center\">\n        <div className=\"text-xs text-gray-500 space-y-1\">\n          {!isConnected && (\n            <p>Please connect to the server first</p>\n          )}\n          {isConnected && callState === CALL_STATES.IDLE && (\n            <p>Click the green phone button to start a call</p>\n          )}\n          {isInCall && (\n            <>\n              <p>Use the microphone button to mute/unmute</p>\n              <p>Click the red phone button to end the call</p>\n            </>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CallControls;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,KAAK,EACLC,QAAQ,EACRC,GAAG,EACHC,MAAM,EACNC,OAAO,EACPC,OAAO,EACPC,OAAO,EACPC,QAAQ,QACH,cAAc;AACrB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,cAAc,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzD,MAAMC,YAAY,GAAGA,CAAC;EACpBC,SAAS;EACTC,WAAW;EACXC,OAAO;EACPC,WAAW;EACXC,SAAS;EACTC,YAAY;EACZC,UAAU,GAAG;AACf,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAE1D,MAAM0B,QAAQ,GAAGV,SAAS,KAAKP,WAAW,CAACkB,OAAO;EAClD,MAAMC,YAAY,GAAGZ,SAAS,KAAKP,WAAW,CAACoB,UAAU;EACzD,MAAMC,QAAQ,GAAGd,SAAS,KAAKP,WAAW,CAACsB,MAAM;EACjD,MAAMC,YAAY,GAAGf,WAAW,IAAID,SAAS,KAAKP,WAAW,CAACwB,IAAI;EAClE,MAAMC,UAAU,GAAGR,QAAQ,IAAIE,YAAY;EAC3C,MAAMO,cAAc,GAAGT,QAAQ,IAAIE,YAAY,CAAC,CAAC;;EAEjD,MAAMQ,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIJ,YAAY,EAAE;MAChBb,WAAW,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAMkB,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIH,UAAU,EAAE;MACdd,SAAS,CAAC,CAAC;IACb;EACF,CAAC;EAED,MAAMkB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIH,cAAc,EAAE;MAClBd,YAAY,CAAC,CAAC;IAChB;EACF,CAAC;EAED,MAAMkB,mBAAmB,GAAGA,CAAA,KAAM;IAChCd,iBAAiB,CAACe,IAAI,IAAI,CAACA,IAAI,CAAC;IAChC;EACF,CAAC;EAED,MAAMC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF;MACA,MAAMC,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC;MAE7E,IAAIH,YAAY,CAACI,KAAK,KAAK,WAAW,EAAE;QACtC,MAAMJ,YAAY,CAACK,MAAM,CAAC,CAAC;MAC7B;;MAEA;MACA,MAAMC,UAAU,GAAGN,YAAY,CAACM,UAAU;MAC1C,MAAMC,QAAQ,GAAG,GAAG;MACpB,MAAMC,SAAS,GAAG,GAAG;MACrB,MAAMC,MAAM,GAAGH,UAAU,GAAGC,QAAQ;MAEpC,MAAMG,WAAW,GAAGV,YAAY,CAACW,YAAY,CAAC,CAAC,EAAEF,MAAM,EAAEH,UAAU,CAAC;MACpE,MAAMM,WAAW,GAAGF,WAAW,CAACG,cAAc,CAAC,CAAC,CAAC;MAEjD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,MAAM,EAAEK,CAAC,EAAE,EAAE;QAC/BF,WAAW,CAACE,CAAC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGD,IAAI,CAACE,EAAE,GAAGT,SAAS,GAAGM,CAAC,GAAGR,UAAU,CAAC,GAAG,GAAG;MAC3E;MAEA,MAAMY,MAAM,GAAGlB,YAAY,CAACmB,kBAAkB,CAAC,CAAC;MAChDD,MAAM,CAACE,MAAM,GAAGV,WAAW;MAC3BQ,MAAM,CAACG,OAAO,CAACrB,YAAY,CAACsB,WAAW,CAAC;MACxCJ,MAAM,CAACK,KAAK,CAAC,CAAC;MAEdC,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;IAC/D,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAAC3C,QAAQ,IAAIR,OAAO,EAAE,OAAO,IAAI;IAErC,MAAMoD,IAAI,GAAG,CAAC;IACd,MAAMC,cAAc,GAAGd,IAAI,CAACe,IAAI,CAAElD,UAAU,GAAG,GAAG,GAAIgD,IAAI,CAAC;IAE3D,oBACE1D,OAAA;MAAK6D,SAAS,EAAC,kCAAkC;MAAAC,QAAA,EAC9CC,KAAK,CAACC,IAAI,CAAC;QAAEzB,MAAM,EAAEmB;MAAK,CAAC,EAAE,CAACO,CAAC,EAAErB,CAAC,kBACjC5C,OAAA;QAEE6D,SAAS,EAAE,uDACTjB,CAAC,GAAGe,cAAc,GACdf,CAAC,GAAG,CAAC,GACH,gBAAgB,GAChBA,CAAC,GAAG,CAAC,GACH,eAAe,GACf,eAAe,GACnB,aAAa;MAChB,GATEA,CAAC;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUP,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;EAED,oBACErE,OAAA;IAAK6D,SAAS,EAAC,UAAU;IAAAC,QAAA,gBACvB9D,OAAA;MAAK6D,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/B9D,OAAA;QAAI6D,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAAa;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3ErE,OAAA;QAAG6D,SAAS,EAAC,uBAAuB;QAAAC,QAAA,GACjC,CAACzD,WAAW,IAAI,oCAAoC,EACpDA,WAAW,IAAID,SAAS,KAAKP,WAAW,CAACwB,IAAI,IAAI,qBAAqB,EACtEL,YAAY,IAAI,uBAAuB,EACvCF,QAAQ,IAAI,kBAAkB,EAC9BI,QAAQ,IAAI,gBAAgB,EAC5Bd,SAAS,KAAKP,WAAW,CAACyE,KAAK,IAAI,YAAY,EAC/ClE,SAAS,KAAKP,WAAW,CAAC0E,KAAK,IAAI,qBAAqB;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNrE,OAAA;MAAK6D,SAAS,EAAC,0BAA0B;MAAAC,QAAA,EACtC,CAAChD,QAAQ,IAAI,CAACE,YAAY,IAAI,CAACE,QAAQ,gBACtClB,OAAA;QACEwE,OAAO,EAAEhD,eAAgB;QACzBiD,QAAQ,EAAE,CAACrD,YAAa;QACxByC,SAAS,EAAE;AACvB;AACA;AACA;AACA,gBAAgBzC,YAAY,GACV,uEAAuE,GACvE,8CAA8C;AAChE,aACc;QAAA0C,QAAA,gBAEF9D,OAAA,CAACX,KAAK;UAACqF,IAAI,EAAE;QAAG;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAClBjD,YAAY,iBACXpB,OAAA;UAAK6D,SAAS,EAAC;QAAsE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAC5F;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,gBAETrE,OAAA;QACEwE,OAAO,EAAE/C,aAAc;QACvBgD,QAAQ,EAAE,CAACnD,UAAW;QACtBuC,SAAS,EAAE;AACvB;AACA;AACA;AACA,gBAAgBvC,UAAU,GACR,oEAAoE,GACpE,8CAA8C;AAChE,aACc;QAAAwC,QAAA,GAED5C,QAAQ,gBACPlB,OAAA,CAACL,OAAO;UAAC+E,IAAI,EAAE,EAAG;UAACb,SAAS,EAAC;QAAc;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE9CrE,OAAA,CAACV,QAAQ;UAACoF,IAAI,EAAE;QAAG;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACtB,EACAvD,QAAQ,iBACPd,OAAA;UAAK6D,SAAS,EAAC;QAAsE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAC5F;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNrE,OAAA;MAAK6D,SAAS,EAAC,+BAA+B;MAAAC,QAAA,gBAE5C9D,OAAA;QAAK6D,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzC9D,OAAA;UACEwE,OAAO,EAAE9C,gBAAiB;UAC1B+C,QAAQ,EAAE,CAAClD,cAAe;UAC1BsC,SAAS,EAAE;AACvB;AACA;AACA;AACA,gBAAgB,CAACtC,cAAc,GACb,8CAA8C,GAC9CjB,OAAO,GACL,yEAAyE,GACzE,iEAAiE;AACrF,aACc;UAAAwD,QAAA,EAEDxD,OAAO,gBAAGN,OAAA,CAACR,MAAM;YAACkF,IAAI,EAAE;UAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGrE,OAAA,CAACT,GAAG;YAACmF,IAAI,EAAE;UAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACTrE,OAAA;UAAK6D,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC9D,OAAA;YAAM6D,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EACpCxD,OAAO,GAAG,OAAO,GAAG;UAAK;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACPrE,OAAA,CAACyD,mBAAmB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrE,OAAA;QAAK6D,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzC9D,OAAA;UACEwE,OAAO,EAAE7C,mBAAoB;UAC7B8C,QAAQ,EAAE,CAAClD,cAAe;UAC1BsC,SAAS,EAAE;AACvB;AACA;AACA;AACA,gBAAgB,CAACtC,cAAc,GACb,8CAA8C,GAC9C,CAACX,cAAc,GACb,yEAAyE,GACzE,iEAAiE;AACrF,aACc;UAAAkD,QAAA,EAEDlD,cAAc,gBAAGZ,OAAA,CAACP,OAAO;YAACiF,IAAI,EAAE;UAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGrE,OAAA,CAACN,OAAO;YAACgF,IAAI,EAAE;UAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACTrE,OAAA;UAAM6D,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EACzClD,cAAc,GAAG,SAAS,GAAG;QAAO;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNrE,OAAA;QAAK6D,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzC9D,OAAA;UACEwE,OAAO,EAAE3C,eAAgB;UACzBgC,SAAS,EAAC,6OAGwD;UAAAC,QAAA,eAElE9D,OAAA,CAACJ,QAAQ;YAAC8E,IAAI,EAAE;UAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACTrE,OAAA;UAAM6D,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAI;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLrD,YAAY,iBACXhB,OAAA;MAAK6D,SAAS,EAAC,iDAAiD;MAAAC,QAAA,gBAC9D9D,OAAA,CAACL,OAAO;QAAC+E,IAAI,EAAE,EAAG;QAACb,SAAS,EAAC;MAA+B;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/DrE,OAAA;QAAM6D,SAAS,EAAC,0BAA0B;QAAAC,QAAA,EAAC;MAAqB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpE,CACN,EAEAvD,QAAQ,iBACPd,OAAA;MAAK6D,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B9D,OAAA;QAAK6D,SAAS,EAAC,mGAAmG;QAAAC,QAAA,gBAChH9D,OAAA;UAAK6D,SAAS,EAAC;QAAmD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzErE,OAAA;UAAA8D,QAAA,EAAM;QAAW;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAjE,SAAS,KAAKP,WAAW,CAAC0E,KAAK,iBAC9BvE,OAAA;MAAK6D,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B9D,OAAA;QAAK6D,SAAS,EAAC,iGAAiG;QAAAC,QAAA,gBAC9G9D,OAAA;UAAK6D,SAAS,EAAC;QAAoC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1DrE,OAAA;UAAA8D,QAAA,EAAM;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDrE,OAAA;MAAK6D,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B9D,OAAA;QAAK6D,SAAS,EAAC,iCAAiC;QAAAC,QAAA,GAC7C,CAACzD,WAAW,iBACXL,OAAA;UAAA8D,QAAA,EAAG;QAAkC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACzC,EACAhE,WAAW,IAAID,SAAS,KAAKP,WAAW,CAACwB,IAAI,iBAC5CrB,OAAA;UAAA8D,QAAA,EAAG;QAA4C;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACnD,EACAvD,QAAQ,iBACPd,OAAA,CAAAE,SAAA;UAAA4D,QAAA,gBACE9D,OAAA;YAAA8D,QAAA,EAAG;UAAwC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/CrE,OAAA;YAAA8D,QAAA,EAAG;UAA0C;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA,eACjD,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1D,EAAA,CApRIR,YAAY;AAAAwE,EAAA,GAAZxE,YAAY;AAsRlB,eAAeA,YAAY;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}