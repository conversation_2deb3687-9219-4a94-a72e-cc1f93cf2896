{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/components/CallControls.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Phone, PhoneOff, Mic, MicOff, Volume2, VolumeX, Loader2, TestTube } from 'lucide-react';\nimport { CALL_STATES } from '../hooks/useVoiceSocket';\nimport { audioProcessor } from '../utils/audioProcessor';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CallControls = ({\n  callState,\n  isConnected,\n  isMuted,\n  onStartCall,\n  onEndCall,\n  onToggleMute,\n  audioLevel = 0\n}) => {\n  _s();\n  const [speakerEnabled, setSpeakerEnabled] = useState(true);\n  const isInCall = callState === CALL_STATES.IN_CALL;\n  const isConnecting = callState === CALL_STATES.CONNECTING;\n  const isEnding = callState === CALL_STATES.ENDING;\n  const canStartCall = isConnected && callState === CALL_STATES.IDLE;\n  const canEndCall = isInCall || isConnecting;\n  const canUseControls = isInCall || isConnecting; // Enable controls when call is active\n\n  const handleStartCall = () => {\n    if (canStartCall) {\n      onStartCall();\n    }\n  };\n  const handleEndCall = () => {\n    if (canEndCall) {\n      onEndCall();\n    }\n  };\n  const handleToggleMute = () => {\n    if (canUseControls) {\n      onToggleMute();\n    }\n  };\n  const handleToggleSpeaker = () => {\n    setSpeakerEnabled(prev => !prev);\n    // In a real implementation, you would control audio output routing here\n  };\n\n  // Audio level visualization\n  const AudioLevelIndicator = () => {\n    if (!isInCall || isMuted) return null;\n    const bars = 5;\n    const activeBarCount = Math.ceil(audioLevel / 100 * bars);\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-1 ml-2\",\n      children: Array.from({\n        length: bars\n      }, (_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `w-1 h-4 rounded-full transition-colors duration-150 ${i < activeBarCount ? i < 2 ? 'bg-success-500' : i < 4 ? 'bg-yellow-500' : 'bg-danger-500' : 'bg-gray-300'}`\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-semibold text-gray-900 mb-2\",\n        children: \"Call Controls\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-600\",\n        children: [!isConnected && 'Connect to server to start calling', isConnected && callState === CALL_STATES.IDLE && 'Ready to start call', isConnecting && 'Connecting to call...', isInCall && 'Call in progress', isEnding && 'Ending call...', callState === CALL_STATES.ENDED && 'Call ended', callState === CALL_STATES.ERROR && 'Call error occurred']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center mb-6\",\n      children: !isInCall && !isConnecting && !isEnding ? /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleStartCall,\n        disabled: !canStartCall,\n        className: `\n              relative w-16 h-16 rounded-full flex items-center justify-center\n              transition-all duration-200 transform hover:scale-105 focus:outline-none\n              focus:ring-4 focus:ring-offset-2 shadow-lg\n              ${canStartCall ? 'bg-success-600 hover:bg-success-700 focus:ring-success-500 text-white' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}\n            `,\n        children: [/*#__PURE__*/_jsxDEV(Phone, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 13\n        }, this), canStartCall && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 rounded-full bg-success-600 animate-ping opacity-20\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleEndCall,\n        disabled: !canEndCall,\n        className: `\n              relative w-16 h-16 rounded-full flex items-center justify-center\n              transition-all duration-200 transform hover:scale-105 focus:outline-none\n              focus:ring-4 focus:ring-offset-2 shadow-lg\n              ${canEndCall ? 'bg-danger-600 hover:bg-danger-700 focus:ring-danger-500 text-white' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}\n            `,\n        children: [isEnding ? /*#__PURE__*/_jsxDEV(Loader2, {\n          size: 24,\n          className: \"animate-spin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(PhoneOff, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 15\n        }, this), isInCall && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 rounded-full bg-danger-600 animate-pulse opacity-30\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center space-x-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleToggleMute,\n          disabled: !canUseControls,\n          className: `\n              w-12 h-12 rounded-full flex items-center justify-center\n              transition-all duration-200 transform hover:scale-105 focus:outline-none\n              focus:ring-2 focus:ring-offset-2 shadow-md\n              ${!canUseControls ? 'bg-gray-200 text-gray-400 cursor-not-allowed' : isMuted ? 'bg-danger-100 text-danger-600 hover:bg-danger-200 focus:ring-danger-500' : 'bg-gray-100 text-gray-600 hover:bg-gray-200 focus:ring-gray-500'}\n            `,\n          children: isMuted ? /*#__PURE__*/_jsxDEV(MicOff, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 24\n          }, this) : /*#__PURE__*/_jsxDEV(Mic, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mt-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-500\",\n            children: isMuted ? 'Muted' : 'Mic'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(AudioLevelIndicator, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleToggleSpeaker,\n          disabled: !canUseControls,\n          className: `\n              w-12 h-12 rounded-full flex items-center justify-center\n              transition-all duration-200 transform hover:scale-105 focus:outline-none\n              focus:ring-2 focus:ring-offset-2 shadow-md\n              ${!canUseControls ? 'bg-gray-200 text-gray-400 cursor-not-allowed' : !speakerEnabled ? 'bg-danger-100 text-danger-600 hover:bg-danger-200 focus:ring-danger-500' : 'bg-gray-100 text-gray-600 hover:bg-gray-200 focus:ring-gray-500'}\n            `,\n          children: speakerEnabled ? /*#__PURE__*/_jsxDEV(Volume2, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 31\n          }, this) : /*#__PURE__*/_jsxDEV(VolumeX, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 55\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs text-gray-500 mt-1\",\n          children: speakerEnabled ? 'Speaker' : 'Muted'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this), isConnecting && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 flex items-center justify-center space-x-2\",\n      children: [/*#__PURE__*/_jsxDEV(Loader2, {\n        size: 16,\n        className: \"animate-spin text-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-sm text-primary-600\",\n        children: \"Connecting to call...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 9\n    }, this), isInCall && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"inline-flex items-center space-x-2 px-3 py-1 bg-success-100 text-success-800 rounded-full text-sm\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-2 h-2 bg-success-500 rounded-full animate-pulse\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Call Active\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 9\n    }, this), callState === CALL_STATES.ERROR && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"inline-flex items-center space-x-2 px-3 py-1 bg-danger-100 text-danger-800 rounded-full text-sm\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-2 h-2 bg-danger-500 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Call Error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-xs text-gray-500 space-y-1\",\n        children: [!isConnected && /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Please connect to the server first\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 13\n        }, this), isConnected && callState === CALL_STATES.IDLE && /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Click the green phone button to start a call\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 13\n        }, this), isInCall && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Use the microphone button to mute/unmute\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Click the red phone button to end the call\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 84,\n    columnNumber: 5\n  }, this);\n};\n_s(CallControls, \"TwGL6T/geUdOy0ow2aEFHNtpaaI=\");\n_c = CallControls;\nexport default CallControls;\nvar _c;\n$RefreshReg$(_c, \"CallControls\");", "map": {"version": 3, "names": ["React", "useState", "Phone", "PhoneOff", "Mic", "<PERSON><PERSON><PERSON><PERSON>", "Volume2", "VolumeX", "Loader2", "TestTube", "CALL_STATES", "audioProcessor", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CallControls", "callState", "isConnected", "isMuted", "onStartCall", "onEndCall", "onToggleMute", "audioLevel", "_s", "speakerEnabled", "setSpeakerEnabled", "isInCall", "IN_CALL", "isConnecting", "CONNECTING", "isEnding", "ENDING", "canStartCall", "IDLE", "canEndCall", "canUseControls", "handleStartCall", "handleEndCall", "handleToggleMute", "handleToggleSpeaker", "prev", "AudioLevelIndicator", "bars", "activeBarCount", "Math", "ceil", "className", "children", "Array", "from", "length", "_", "i", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ENDED", "ERROR", "onClick", "disabled", "size", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/components/CallControls.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Phone,\n  PhoneOff,\n  Mic,\n  MicOff,\n  Volume2,\n  VolumeX,\n  Loader2,\n  TestTube\n} from 'lucide-react';\nimport { CALL_STATES } from '../hooks/useVoiceSocket';\nimport { audioProcessor } from '../utils/audioProcessor';\n\nconst CallControls = ({ \n  callState, \n  isConnected, \n  isMuted, \n  onStartCall, \n  onEndCall, \n  onToggleMute,\n  audioLevel = 0 \n}) => {\n  const [speakerEnabled, setSpeakerEnabled] = useState(true);\n\n  const isInCall = callState === CALL_STATES.IN_CALL;\n  const isConnecting = callState === CALL_STATES.CONNECTING;\n  const isEnding = callState === CALL_STATES.ENDING;\n  const canStartCall = isConnected && callState === CALL_STATES.IDLE;\n  const canEndCall = isInCall || isConnecting;\n  const canUseControls = isInCall || isConnecting; // Enable controls when call is active\n\n  const handleStartCall = () => {\n    if (canStartCall) {\n      onStartCall();\n    }\n  };\n\n  const handleEndCall = () => {\n    if (canEndCall) {\n      onEndCall();\n    }\n  };\n\n  const handleToggleMute = () => {\n    if (canUseControls) {\n      onToggleMute();\n    }\n  };\n\n  const handleToggleSpeaker = () => {\n    setSpeakerEnabled(prev => !prev);\n    // In a real implementation, you would control audio output routing here\n  };\n\n  // Audio level visualization\n  const AudioLevelIndicator = () => {\n    if (!isInCall || isMuted) return null;\n\n    const bars = 5;\n    const activeBarCount = Math.ceil((audioLevel / 100) * bars);\n\n    return (\n      <div className=\"flex items-center space-x-1 ml-2\">\n        {Array.from({ length: bars }, (_, i) => (\n          <div\n            key={i}\n            className={`w-1 h-4 rounded-full transition-colors duration-150 ${\n              i < activeBarCount \n                ? i < 2 \n                  ? 'bg-success-500' \n                  : i < 4 \n                    ? 'bg-yellow-500' \n                    : 'bg-danger-500'\n                : 'bg-gray-300'\n            }`}\n          />\n        ))}\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"card p-6\">\n      <div className=\"text-center mb-6\">\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">Call Controls</h2>\n        <p className=\"text-sm text-gray-600\">\n          {!isConnected && 'Connect to server to start calling'}\n          {isConnected && callState === CALL_STATES.IDLE && 'Ready to start call'}\n          {isConnecting && 'Connecting to call...'}\n          {isInCall && 'Call in progress'}\n          {isEnding && 'Ending call...'}\n          {callState === CALL_STATES.ENDED && 'Call ended'}\n          {callState === CALL_STATES.ERROR && 'Call error occurred'}\n        </p>\n      </div>\n\n      {/* Main Call Button */}\n      <div className=\"flex justify-center mb-6\">\n        {!isInCall && !isConnecting && !isEnding ? (\n          <button\n            onClick={handleStartCall}\n            disabled={!canStartCall}\n            className={`\n              relative w-16 h-16 rounded-full flex items-center justify-center\n              transition-all duration-200 transform hover:scale-105 focus:outline-none\n              focus:ring-4 focus:ring-offset-2 shadow-lg\n              ${canStartCall \n                ? 'bg-success-600 hover:bg-success-700 focus:ring-success-500 text-white' \n                : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n              }\n            `}\n          >\n            <Phone size={24} />\n            {canStartCall && (\n              <div className=\"absolute inset-0 rounded-full bg-success-600 animate-ping opacity-20\"></div>\n            )}\n          </button>\n        ) : (\n          <button\n            onClick={handleEndCall}\n            disabled={!canEndCall}\n            className={`\n              relative w-16 h-16 rounded-full flex items-center justify-center\n              transition-all duration-200 transform hover:scale-105 focus:outline-none\n              focus:ring-4 focus:ring-offset-2 shadow-lg\n              ${canEndCall \n                ? 'bg-danger-600 hover:bg-danger-700 focus:ring-danger-500 text-white' \n                : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n              }\n            `}\n          >\n            {isEnding ? (\n              <Loader2 size={24} className=\"animate-spin\" />\n            ) : (\n              <PhoneOff size={24} />\n            )}\n            {isInCall && (\n              <div className=\"absolute inset-0 rounded-full bg-danger-600 animate-pulse opacity-30\"></div>\n            )}\n          </button>\n        )}\n      </div>\n\n      {/* Secondary Controls */}\n      <div className=\"flex justify-center space-x-4\">\n        {/* Microphone Control */}\n        <div className=\"flex flex-col items-center\">\n          <button\n            onClick={handleToggleMute}\n            disabled={!canUseControls}\n            className={`\n              w-12 h-12 rounded-full flex items-center justify-center\n              transition-all duration-200 transform hover:scale-105 focus:outline-none\n              focus:ring-2 focus:ring-offset-2 shadow-md\n              ${!canUseControls\n                ? 'bg-gray-200 text-gray-400 cursor-not-allowed'\n                : isMuted\n                  ? 'bg-danger-100 text-danger-600 hover:bg-danger-200 focus:ring-danger-500'\n                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200 focus:ring-gray-500'\n              }\n            `}\n          >\n            {isMuted ? <MicOff size={20} /> : <Mic size={20} />}\n          </button>\n          <div className=\"flex items-center mt-1\">\n            <span className=\"text-xs text-gray-500\">\n              {isMuted ? 'Muted' : 'Mic'}\n            </span>\n            <AudioLevelIndicator />\n          </div>\n        </div>\n\n        {/* Speaker Control */}\n        <div className=\"flex flex-col items-center\">\n          <button\n            onClick={handleToggleSpeaker}\n            disabled={!canUseControls}\n            className={`\n              w-12 h-12 rounded-full flex items-center justify-center\n              transition-all duration-200 transform hover:scale-105 focus:outline-none\n              focus:ring-2 focus:ring-offset-2 shadow-md\n              ${!canUseControls\n                ? 'bg-gray-200 text-gray-400 cursor-not-allowed'\n                : !speakerEnabled\n                  ? 'bg-danger-100 text-danger-600 hover:bg-danger-200 focus:ring-danger-500'\n                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200 focus:ring-gray-500'\n              }\n            `}\n          >\n            {speakerEnabled ? <Volume2 size={20} /> : <VolumeX size={20} />}\n          </button>\n          <span className=\"text-xs text-gray-500 mt-1\">\n            {speakerEnabled ? 'Speaker' : 'Muted'}\n          </span>\n        </div>\n      </div>\n\n      {/* Call Status Indicators */}\n      {isConnecting && (\n        <div className=\"mt-6 flex items-center justify-center space-x-2\">\n          <Loader2 size={16} className=\"animate-spin text-primary-600\" />\n          <span className=\"text-sm text-primary-600\">Connecting to call...</span>\n        </div>\n      )}\n\n      {isInCall && (\n        <div className=\"mt-6 text-center\">\n          <div className=\"inline-flex items-center space-x-2 px-3 py-1 bg-success-100 text-success-800 rounded-full text-sm\">\n            <div className=\"w-2 h-2 bg-success-500 rounded-full animate-pulse\"></div>\n            <span>Call Active</span>\n          </div>\n        </div>\n      )}\n\n      {callState === CALL_STATES.ERROR && (\n        <div className=\"mt-6 text-center\">\n          <div className=\"inline-flex items-center space-x-2 px-3 py-1 bg-danger-100 text-danger-800 rounded-full text-sm\">\n            <div className=\"w-2 h-2 bg-danger-500 rounded-full\"></div>\n            <span>Call Error</span>\n          </div>\n        </div>\n      )}\n\n      {/* Instructions */}\n      <div className=\"mt-6 text-center\">\n        <div className=\"text-xs text-gray-500 space-y-1\">\n          {!isConnected && (\n            <p>Please connect to the server first</p>\n          )}\n          {isConnected && callState === CALL_STATES.IDLE && (\n            <p>Click the green phone button to start a call</p>\n          )}\n          {isInCall && (\n            <>\n              <p>Use the microphone button to mute/unmute</p>\n              <p>Click the red phone button to end the call</p>\n            </>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CallControls;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,KAAK,EACLC,QAAQ,EACRC,GAAG,EACHC,MAAM,EACNC,OAAO,EACPC,OAAO,EACPC,OAAO,EACPC,QAAQ,QACH,cAAc;AACrB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,cAAc,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzD,MAAMC,YAAY,GAAGA,CAAC;EACpBC,SAAS;EACTC,WAAW;EACXC,OAAO;EACPC,WAAW;EACXC,SAAS;EACTC,YAAY;EACZC,UAAU,GAAG;AACf,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAE1D,MAAM0B,QAAQ,GAAGV,SAAS,KAAKP,WAAW,CAACkB,OAAO;EAClD,MAAMC,YAAY,GAAGZ,SAAS,KAAKP,WAAW,CAACoB,UAAU;EACzD,MAAMC,QAAQ,GAAGd,SAAS,KAAKP,WAAW,CAACsB,MAAM;EACjD,MAAMC,YAAY,GAAGf,WAAW,IAAID,SAAS,KAAKP,WAAW,CAACwB,IAAI;EAClE,MAAMC,UAAU,GAAGR,QAAQ,IAAIE,YAAY;EAC3C,MAAMO,cAAc,GAAGT,QAAQ,IAAIE,YAAY,CAAC,CAAC;;EAEjD,MAAMQ,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIJ,YAAY,EAAE;MAChBb,WAAW,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAMkB,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIH,UAAU,EAAE;MACdd,SAAS,CAAC,CAAC;IACb;EACF,CAAC;EAED,MAAMkB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIH,cAAc,EAAE;MAClBd,YAAY,CAAC,CAAC;IAChB;EACF,CAAC;EAED,MAAMkB,mBAAmB,GAAGA,CAAA,KAAM;IAChCd,iBAAiB,CAACe,IAAI,IAAI,CAACA,IAAI,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAACf,QAAQ,IAAIR,OAAO,EAAE,OAAO,IAAI;IAErC,MAAMwB,IAAI,GAAG,CAAC;IACd,MAAMC,cAAc,GAAGC,IAAI,CAACC,IAAI,CAAEvB,UAAU,GAAG,GAAG,GAAIoB,IAAI,CAAC;IAE3D,oBACE9B,OAAA;MAAKkC,SAAS,EAAC,kCAAkC;MAAAC,QAAA,EAC9CC,KAAK,CAACC,IAAI,CAAC;QAAEC,MAAM,EAAER;MAAK,CAAC,EAAE,CAACS,CAAC,EAAEC,CAAC,kBACjCxC,OAAA;QAEEkC,SAAS,EAAE,uDACTM,CAAC,GAAGT,cAAc,GACdS,CAAC,GAAG,CAAC,GACH,gBAAgB,GAChBA,CAAC,GAAG,CAAC,GACH,eAAe,GACf,eAAe,GACnB,aAAa;MAChB,GATEA,CAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUP,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;EAED,oBACE5C,OAAA;IAAKkC,SAAS,EAAC,UAAU;IAAAC,QAAA,gBACvBnC,OAAA;MAAKkC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BnC,OAAA;QAAIkC,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAAa;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3E5C,OAAA;QAAGkC,SAAS,EAAC,uBAAuB;QAAAC,QAAA,GACjC,CAAC9B,WAAW,IAAI,oCAAoC,EACpDA,WAAW,IAAID,SAAS,KAAKP,WAAW,CAACwB,IAAI,IAAI,qBAAqB,EACtEL,YAAY,IAAI,uBAAuB,EACvCF,QAAQ,IAAI,kBAAkB,EAC9BI,QAAQ,IAAI,gBAAgB,EAC5Bd,SAAS,KAAKP,WAAW,CAACgD,KAAK,IAAI,YAAY,EAC/CzC,SAAS,KAAKP,WAAW,CAACiD,KAAK,IAAI,qBAAqB;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGN5C,OAAA;MAAKkC,SAAS,EAAC,0BAA0B;MAAAC,QAAA,EACtC,CAACrB,QAAQ,IAAI,CAACE,YAAY,IAAI,CAACE,QAAQ,gBACtClB,OAAA;QACE+C,OAAO,EAAEvB,eAAgB;QACzBwB,QAAQ,EAAE,CAAC5B,YAAa;QACxBc,SAAS,EAAE;AACvB;AACA;AACA;AACA,gBAAgBd,YAAY,GACV,uEAAuE,GACvE,8CAA8C;AAChE,aACc;QAAAe,QAAA,gBAEFnC,OAAA,CAACX,KAAK;UAAC4D,IAAI,EAAE;QAAG;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAClBxB,YAAY,iBACXpB,OAAA;UAAKkC,SAAS,EAAC;QAAsE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAC5F;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,gBAET5C,OAAA;QACE+C,OAAO,EAAEtB,aAAc;QACvBuB,QAAQ,EAAE,CAAC1B,UAAW;QACtBY,SAAS,EAAE;AACvB;AACA;AACA;AACA,gBAAgBZ,UAAU,GACR,oEAAoE,GACpE,8CAA8C;AAChE,aACc;QAAAa,QAAA,GAEDjB,QAAQ,gBACPlB,OAAA,CAACL,OAAO;UAACsD,IAAI,EAAE,EAAG;UAACf,SAAS,EAAC;QAAc;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE9C5C,OAAA,CAACV,QAAQ;UAAC2D,IAAI,EAAE;QAAG;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACtB,EACA9B,QAAQ,iBACPd,OAAA;UAAKkC,SAAS,EAAC;QAAsE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAC5F;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN5C,OAAA;MAAKkC,SAAS,EAAC,+BAA+B;MAAAC,QAAA,gBAE5CnC,OAAA;QAAKkC,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzCnC,OAAA;UACE+C,OAAO,EAAErB,gBAAiB;UAC1BsB,QAAQ,EAAE,CAACzB,cAAe;UAC1BW,SAAS,EAAE;AACvB;AACA;AACA;AACA,gBAAgB,CAACX,cAAc,GACb,8CAA8C,GAC9CjB,OAAO,GACL,yEAAyE,GACzE,iEAAiE;AACrF,aACc;UAAA6B,QAAA,EAED7B,OAAO,gBAAGN,OAAA,CAACR,MAAM;YAACyD,IAAI,EAAE;UAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG5C,OAAA,CAACT,GAAG;YAAC0D,IAAI,EAAE;UAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACT5C,OAAA;UAAKkC,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCnC,OAAA;YAAMkC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EACpC7B,OAAO,GAAG,OAAO,GAAG;UAAK;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACP5C,OAAA,CAAC6B,mBAAmB;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5C,OAAA;QAAKkC,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzCnC,OAAA;UACE+C,OAAO,EAAEpB,mBAAoB;UAC7BqB,QAAQ,EAAE,CAACzB,cAAe;UAC1BW,SAAS,EAAE;AACvB;AACA;AACA;AACA,gBAAgB,CAACX,cAAc,GACb,8CAA8C,GAC9C,CAACX,cAAc,GACb,yEAAyE,GACzE,iEAAiE;AACrF,aACc;UAAAuB,QAAA,EAEDvB,cAAc,gBAAGZ,OAAA,CAACP,OAAO;YAACwD,IAAI,EAAE;UAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG5C,OAAA,CAACN,OAAO;YAACuD,IAAI,EAAE;UAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACT5C,OAAA;UAAMkC,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EACzCvB,cAAc,GAAG,SAAS,GAAG;QAAO;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL5B,YAAY,iBACXhB,OAAA;MAAKkC,SAAS,EAAC,iDAAiD;MAAAC,QAAA,gBAC9DnC,OAAA,CAACL,OAAO;QAACsD,IAAI,EAAE,EAAG;QAACf,SAAS,EAAC;MAA+B;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/D5C,OAAA;QAAMkC,SAAS,EAAC,0BAA0B;QAAAC,QAAA,EAAC;MAAqB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpE,CACN,EAEA9B,QAAQ,iBACPd,OAAA;MAAKkC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BnC,OAAA;QAAKkC,SAAS,EAAC,mGAAmG;QAAAC,QAAA,gBAChHnC,OAAA;UAAKkC,SAAS,EAAC;QAAmD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzE5C,OAAA;UAAAmC,QAAA,EAAM;QAAW;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAxC,SAAS,KAAKP,WAAW,CAACiD,KAAK,iBAC9B9C,OAAA;MAAKkC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BnC,OAAA;QAAKkC,SAAS,EAAC,iGAAiG;QAAAC,QAAA,gBAC9GnC,OAAA;UAAKkC,SAAS,EAAC;QAAoC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1D5C,OAAA;UAAAmC,QAAA,EAAM;QAAU;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD5C,OAAA;MAAKkC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BnC,OAAA;QAAKkC,SAAS,EAAC,iCAAiC;QAAAC,QAAA,GAC7C,CAAC9B,WAAW,iBACXL,OAAA;UAAAmC,QAAA,EAAG;QAAkC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACzC,EACAvC,WAAW,IAAID,SAAS,KAAKP,WAAW,CAACwB,IAAI,iBAC5CrB,OAAA;UAAAmC,QAAA,EAAG;QAA4C;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACnD,EACA9B,QAAQ,iBACPd,OAAA,CAAAE,SAAA;UAAAiC,QAAA,gBACEnC,OAAA;YAAAmC,QAAA,EAAG;UAAwC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/C5C,OAAA;YAAAmC,QAAA,EAAG;UAA0C;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA,eACjD,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjC,EAAA,CArOIR,YAAY;AAAA+C,EAAA,GAAZ/C,YAAY;AAuOlB,eAAeA,YAAY;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}