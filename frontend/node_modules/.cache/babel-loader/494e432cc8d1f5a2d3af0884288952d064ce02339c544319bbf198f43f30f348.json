{"ast": null, "code": "/**\n * Audio Processing Utilities for WebSocket Voice Protocol\n * Handles µ-law encoding/decoding, resampling, and audio format conversions\n */\n\n// µ-law encoding/decoding tables for better performance\nconst MULAW_BIAS = 0x84;\nconst MULAW_CLIP = 32635;\n\n// Pre-computed µ-law encoding table\nconst muLawEncodeTable = new Array(256);\nconst muLawDecodeTable = new Array(256);\n\n// Initialize µ-law tables\nfunction initializeMuLawTables() {\n  // Encoding table\n  for (let i = 0; i < 256; i++) {\n    let sample = i - 128;\n    sample = sample * 256; // Scale to 16-bit range\n\n    const sign = sample >> 8 & 0x80;\n    if (sign !== 0) sample = -sample;\n    if (sample > MULAW_CLIP) sample = MULAW_CLIP;\n    sample += MULAW_BIAS;\n    let exponent = 7;\n    for (let exp = 0; exp < 8; exp++) {\n      if (sample <= 0x1F << exp + 3) {\n        exponent = exp;\n        break;\n      }\n    }\n    const mantissa = sample >> exponent + 3 & 0x0F;\n    muLawEncodeTable[i] = ~(sign | exponent << 4 | mantissa);\n  }\n\n  // Decoding table - Standard µ-law decoding algorithm\n  for (let i = 0; i < 256; i++) {\n    const mulaw = ~i;\n    const exponent = (mulaw & 0x70) >> 4;\n    const mantissa = mulaw & 0x0F;\n    let sample;\n    if (exponent === 0) {\n      sample = (mantissa << 4) + 8;\n    } else {\n      sample = (mantissa + 16 << exponent + 3) - 128;\n    }\n\n    // Apply sign\n    if (mulaw & 0x80) {\n      sample = -sample;\n    }\n    muLawDecodeTable[i] = sample;\n  }\n}\n\n// Initialize tables on module load\ninitializeMuLawTables();\n\n/**\n * Audio Processor Class\n */\nexport class AudioProcessor {\n  constructor() {\n    this.sampleRate = 8000; // Protocol requirement\n    this.chunkDuration = 20; // 20ms chunks\n    this.chunkSize = this.sampleRate * this.chunkDuration / 1000; // 160 samples\n    this.audioBuffer = [];\n  }\n\n  /**\n   * Convert PCM Float32Array to µ-law bytes\n   * @param {Float32Array} pcmData - Input PCM data (-1.0 to 1.0)\n   * @returns {Uint8Array} µ-law encoded data\n   */\n  pcmToMuLaw(pcmData) {\n    const muLawData = new Uint8Array(pcmData.length);\n    for (let i = 0; i < pcmData.length; i++) {\n      // Clamp and scale to 16-bit range\n      let sample = Math.max(-1, Math.min(1, pcmData[i]));\n      sample = Math.round(sample * 32767);\n\n      // Apply µ-law encoding\n      const sign = sample < 0 ? 0x80 : 0x00;\n      sample = Math.abs(sample);\n      if (sample > MULAW_CLIP) sample = MULAW_CLIP;\n      sample += MULAW_BIAS;\n      let exponent = 7;\n      for (let exp = 0; exp < 8; exp++) {\n        if (sample <= 0x1F << exp + 3) {\n          exponent = exp;\n          break;\n        }\n      }\n      const mantissa = sample >> exponent + 3 & 0x0F;\n      muLawData[i] = ~(sign | exponent << 4 | mantissa);\n    }\n    return muLawData;\n  }\n\n  /**\n   * Convert µ-law bytes to PCM Float32Array\n   * Standard ITU-T G.711 µ-law decoding algorithm\n   * @param {Uint8Array} muLawData - Input µ-law data\n   * @returns {Float32Array} PCM data (-1.0 to 1.0)\n   */\n  muLawToPcm(muLawData) {\n    const pcmData = new Float32Array(muLawData.length);\n    for (let i = 0; i < muLawData.length; i++) {\n      const mulaw = muLawData[i];\n\n      // Invert the µ-law value (standard G.711 step)\n      const inverted = ~mulaw;\n\n      // Extract components\n      const sign = inverted & 0x80 ? -1 : 1;\n      const exponent = (inverted & 0x70) >> 4;\n      const mantissa = inverted & 0x0F;\n\n      // Standard G.711 µ-law decoding\n      let sample;\n      if (exponent === 0) {\n        sample = (mantissa << 4) + 8;\n      } else {\n        sample = (mantissa + 16 << exponent + 3) - 128;\n      }\n\n      // Apply sign and normalize to -1.0 to 1.0 range for Web Audio API\n      pcmData[i] = sign * sample / 32768.0;\n    }\n    return pcmData;\n  }\n\n  /**\n   * Resample audio data\n   * @param {Float32Array} inputData - Input audio data\n   * @param {number} inputSampleRate - Input sample rate\n   * @param {number} outputSampleRate - Output sample rate\n   * @returns {Float32Array} Resampled audio data\n   */\n  resample(inputData, inputSampleRate, outputSampleRate) {\n    if (inputSampleRate === outputSampleRate) {\n      return inputData;\n    }\n    const ratio = inputSampleRate / outputSampleRate;\n    const outputLength = Math.floor(inputData.length / ratio);\n    const outputData = new Float32Array(outputLength);\n    for (let i = 0; i < outputLength; i++) {\n      const sourceIndex = i * ratio;\n      const index = Math.floor(sourceIndex);\n      const fraction = sourceIndex - index;\n      if (index + 1 < inputData.length) {\n        // Linear interpolation\n        outputData[i] = inputData[index] * (1 - fraction) + inputData[index + 1] * fraction;\n      } else {\n        outputData[i] = inputData[index];\n      }\n    }\n    return outputData;\n  }\n\n  /**\n   * Process audio input for transmission\n   * @param {Float32Array} inputData - Raw audio input\n   * @param {number} inputSampleRate - Input sample rate\n   * @returns {Array<Uint8Array>} Array of µ-law encoded chunks\n   */\n  processInputAudio(inputData, inputSampleRate = 44100) {\n    // Resample to 8kHz if needed\n    let processedData = inputData;\n    if (inputSampleRate !== this.sampleRate) {\n      processedData = this.resample(inputData, inputSampleRate, this.sampleRate);\n    }\n\n    // Add to buffer\n    this.audioBuffer.push(...processedData);\n\n    // Extract chunks\n    const chunks = [];\n    while (this.audioBuffer.length >= this.chunkSize) {\n      const chunk = this.audioBuffer.splice(0, this.chunkSize);\n      const chunkArray = new Float32Array(chunk);\n      const muLawChunk = this.pcmToMuLaw(chunkArray);\n      chunks.push(muLawChunk);\n    }\n    return chunks;\n  }\n\n  /**\n   * Process received audio for playback\n   * @param {string} base64Data - Base64 encoded µ-law data\n   * @returns {AudioBuffer} Web Audio API AudioBuffer\n   */\n  async processReceivedAudio(base64Data, audioContext) {\n    try {\n      console.log('audioProcessor.processReceivedAudio - Input base64 length:', base64Data.length);\n\n      // Decode base64 to µ-law data\n      const binaryString = atob(base64Data);\n      console.log('Decoded binary string length:', binaryString.length);\n      const muLawData = new Uint8Array(binaryString.length);\n      for (let i = 0; i < binaryString.length; i++) {\n        muLawData[i] = binaryString.charCodeAt(i);\n      }\n      console.log('µ-law data created, length:', muLawData.length);\n\n      // Convert µ-law to PCM\n      const pcmData = this.muLawToPcm(muLawData);\n      console.log('PCM data converted, length:', pcmData.length, 'sample values:', pcmData.slice(0, 5));\n\n      // Check for valid audio data\n      const maxValue = Math.max(...pcmData);\n      const minValue = Math.min(...pcmData);\n      const rms = Math.sqrt(pcmData.reduce((sum, val) => sum + val * val, 0) / pcmData.length);\n      console.log('PCM data range - max:', maxValue, 'min:', minValue, 'RMS:', rms);\n\n      // Ensure we have valid audio data\n      if (maxValue === 0 && minValue === 0) {\n        console.warn('Audio data appears to be silent (all zeros)');\n      }\n\n      // Create AudioBuffer using AudioContext sample rate, not protocol sample rate\n      console.log('Creating AudioBuffer with AudioContext sample rate:', audioContext.sampleRate, 'vs protocol rate:', this.sampleRate);\n      console.log('PCM data length:', pcmData.length, 'original duration:', pcmData.length / this.sampleRate, 'seconds');\n\n      // Resample PCM data from 8kHz to AudioContext sample rate\n      const targetSampleRate = audioContext.sampleRate;\n      const resampledData = this.resample(pcmData, this.sampleRate, targetSampleRate);\n\n      // Ensure we have a valid length for the audio buffer\n      if (resampledData.length === 0) {\n        console.error('Resampled data is empty');\n        return null;\n      }\n      const audioBuffer = audioContext.createBuffer(1, resampledData.length, targetSampleRate);\n      audioBuffer.getChannelData(0).set(resampledData);\n      console.log('AudioBuffer created with resampling - new length:', resampledData.length, 'duration:', audioBuffer.duration, 'seconds');\n      return audioBuffer;\n    } catch (error) {\n      console.error('Error processing received audio:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Convert audio data to base64 for transmission\n   * @param {Uint8Array} muLawData - µ-law encoded data\n   * @returns {string} Base64 encoded string\n   */\n  encodeToBase64(muLawData) {\n    let binaryString = '';\n    for (let i = 0; i < muLawData.length; i++) {\n      binaryString += String.fromCharCode(muLawData[i]);\n    }\n    return btoa(binaryString);\n  }\n\n  /**\n   * Calculate audio level (RMS) for visualization\n   * @param {Float32Array} audioData - Audio data\n   * @returns {number} Audio level (0-100)\n   */\n  calculateAudioLevel(audioData) {\n    let sum = 0;\n    for (let i = 0; i < audioData.length; i++) {\n      sum += audioData[i] * audioData[i];\n    }\n    const rms = Math.sqrt(sum / audioData.length);\n    return Math.min(100, rms * 100);\n  }\n\n  /**\n   * Apply noise gate to reduce background noise\n   * @param {Float32Array} audioData - Input audio data\n   * @param {number} threshold - Noise gate threshold (0-1)\n   * @returns {Float32Array} Processed audio data\n   */\n  applyNoiseGate(audioData, threshold = 0.01) {\n    const processedData = new Float32Array(audioData.length);\n    for (let i = 0; i < audioData.length; i++) {\n      const sample = Math.abs(audioData[i]);\n      if (sample > threshold) {\n        processedData[i] = audioData[i];\n      } else {\n        processedData[i] = 0;\n      }\n    }\n    return processedData;\n  }\n\n  /**\n   * Clear internal audio buffer\n   */\n  clearBuffer() {\n    this.audioBuffer = [];\n  }\n\n  /**\n   * Get buffer size\n   * @returns {number} Current buffer size\n   */\n  getBufferSize() {\n    return this.audioBuffer.length;\n  }\n}\n\n/**\n * Audio Queue for managing playback timing\n */\nexport class AudioQueue {\n  constructor(audioContext) {\n    this.audioContext = audioContext;\n    this.queue = [];\n    this.isPlaying = false;\n    this.nextStartTime = 0;\n  }\n\n  /**\n   * Add audio buffer to queue\n   * @param {AudioBuffer} audioBuffer - Audio buffer to queue\n   */\n  enqueue(audioBuffer) {\n    console.log('AudioQueue.enqueue - Adding buffer with duration:', audioBuffer.duration, 'seconds');\n    this.queue.push(audioBuffer);\n    console.log('Queue length now:', this.queue.length, 'isPlaying:', this.isPlaying);\n    if (!this.isPlaying) {\n      this.playNext();\n    }\n  }\n\n  /**\n   * Play next audio buffer in queue\n   */\n  playNext() {\n    if (this.queue.length === 0) {\n      console.log('AudioQueue.playNext - Queue empty, stopping playback');\n      this.isPlaying = false;\n      return;\n    }\n    console.log('AudioQueue.playNext - Playing next buffer, queue length:', this.queue.length);\n    this.isPlaying = true;\n    const audioBuffer = this.queue.shift();\n\n    // Verify AudioContext state\n    console.log('AudioQueue.playNext - AudioContext state:', this.audioContext.state, 'Sample rate:', this.audioContext.sampleRate);\n    const source = this.audioContext.createBufferSource();\n    source.buffer = audioBuffer;\n\n    // Create a gain node for better control\n    const gainNode = this.audioContext.createGain();\n    gainNode.gain.value = 1.0; // Full volume\n\n    // Connect: source -> gain -> destination\n    source.connect(gainNode);\n    gainNode.connect(this.audioContext.destination);\n    const currentTime = this.audioContext.currentTime;\n    const startTime = Math.max(currentTime, this.nextStartTime);\n    console.log('AudioQueue.playNext - Starting playback at time:', startTime, 'duration:', audioBuffer.duration);\n    console.log('AudioQueue.playNext - Buffer details: channels:', audioBuffer.numberOfChannels, 'length:', audioBuffer.length, 'sampleRate:', audioBuffer.sampleRate);\n\n    // Check if audio data has content\n    const channelData = audioBuffer.getChannelData(0);\n    const maxAmplitude = Math.max(...channelData.slice(0, Math.min(1000, channelData.length)));\n    const minAmplitude = Math.min(...channelData.slice(0, Math.min(1000, channelData.length)));\n    console.log('AudioQueue.playNext - Audio amplitude check - max:', maxAmplitude, 'min:', minAmplitude, 'samples preview:', channelData.slice(0, 5));\n\n    // Ensure AudioContext is running\n    if (this.audioContext.state !== 'running') {\n      console.warn('AudioContext is not running, attempting to resume...');\n      this.audioContext.resume().then(() => {\n        console.log('AudioContext resumed successfully');\n      });\n    }\n    source.start(startTime);\n    this.nextStartTime = startTime + audioBuffer.duration;\n    source.onended = () => {\n      console.log('AudioQueue.playNext - Audio ended, playing next...');\n      this.playNext();\n    };\n  }\n\n  /**\n   * Clear the audio queue\n   */\n  clear() {\n    this.queue = [];\n    this.isPlaying = false;\n    this.nextStartTime = 0;\n  }\n\n  /**\n   * Test audio playback with a simple tone\n   */\n  testAudioPlayback() {\n    if (!this.audioContext) {\n      console.error('AudioContext not available for test');\n      return;\n    }\n    console.log('Testing audio playback with 440Hz tone...');\n\n    // Create a 1-second 440Hz sine wave\n    const sampleRate = this.audioContext.sampleRate;\n    const duration = 1.0; // 1 second\n    const frequency = 440; // A4 note\n    const length = sampleRate * duration;\n    const audioBuffer = this.audioContext.createBuffer(1, length, sampleRate);\n    const channelData = audioBuffer.getChannelData(0);\n    for (let i = 0; i < length; i++) {\n      channelData[i] = Math.sin(2 * Math.PI * frequency * i / sampleRate) * 0.3;\n    }\n    const source = this.audioContext.createBufferSource();\n    source.buffer = audioBuffer;\n    source.connect(this.audioContext.destination);\n    source.start();\n    console.log('Test tone started - should hear 440Hz for 1 second');\n  }\n\n  /**\n   * Test µ-law decoding with a simple pattern\n   */\n  testMuLawDecoding() {\n    // Create a simple test pattern\n    const testData = new Uint8Array([0x00, 0x80, 0xFF, 0x7F, 0x40, 0xC0]);\n    const decoded = this.muLawToPcm(testData);\n    console.log('µ-law test - input:', Array.from(testData), 'decoded:', Array.from(decoded));\n    return decoded;\n  }\n}\n\n// Export singleton instance\nexport const audioProcessor = new AudioProcessor();\n\n// Expose for debugging in browser console\nif (typeof window !== 'undefined') {\n  window.audioProcessor = audioProcessor;\n  window.AudioQueue = AudioQueue;\n}", "map": {"version": 3, "names": ["MULAW_BIAS", "MULAW_CLIP", "muLawEncodeTable", "Array", "muLawDecodeTable", "initializeMuLawTables", "i", "sample", "sign", "exponent", "exp", "mantissa", "mulaw", "AudioProcessor", "constructor", "sampleRate", "chunkDuration", "chunkSize", "audioBuffer", "pcmToMuLaw", "pcmData", "muLawData", "Uint8Array", "length", "Math", "max", "min", "round", "abs", "muLawToPcm", "Float32Array", "inverted", "resample", "inputData", "inputSampleRate", "outputSampleRate", "ratio", "outputLength", "floor", "outputData", "sourceIndex", "index", "fraction", "processInputAudio", "processedData", "push", "chunks", "chunk", "splice", "chunkArray", "muLawChunk", "processReceivedAudio", "base64Data", "audioContext", "console", "log", "binaryString", "atob", "charCodeAt", "slice", "maxValue", "minValue", "rms", "sqrt", "reduce", "sum", "val", "warn", "targetSampleRate", "resampledData", "error", "createBuffer", "getChannelData", "set", "duration", "encodeToBase64", "String", "fromCharCode", "btoa", "calculateAudioLevel", "audioData", "applyNoiseGate", "threshold", "<PERSON><PERSON><PERSON><PERSON>", "getBufferSize", "AudioQueue", "queue", "isPlaying", "nextStartTime", "enqueue", "playNext", "shift", "state", "source", "createBufferSource", "buffer", "gainNode", "createGain", "gain", "value", "connect", "destination", "currentTime", "startTime", "numberOfChannels", "channelData", "maxAmplitude", "minAmplitude", "resume", "then", "start", "onended", "clear", "testAudioPlayback", "frequency", "sin", "PI", "testMuLawDecoding", "testData", "decoded", "from", "audioProcessor", "window"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/utils/audioProcessor.js"], "sourcesContent": ["/**\n * Audio Processing Utilities for WebSocket Voice Protocol\n * Handles µ-law encoding/decoding, resampling, and audio format conversions\n */\n\n// µ-law encoding/decoding tables for better performance\nconst MULAW_BIAS = 0x84;\nconst MULAW_CLIP = 32635;\n\n// Pre-computed µ-law encoding table\nconst muLawEncodeTable = new Array(256);\nconst muLawDecodeTable = new Array(256);\n\n// Initialize µ-law tables\nfunction initializeMuLawTables() {\n  // Encoding table\n  for (let i = 0; i < 256; i++) {\n    let sample = i - 128;\n    sample = sample * 256; // Scale to 16-bit range\n    \n    const sign = (sample >> 8) & 0x80;\n    if (sign !== 0) sample = -sample;\n    if (sample > MULAW_CLIP) sample = MULAW_CLIP;\n    \n    sample += MULAW_BIAS;\n    let exponent = 7;\n    for (let exp = 0; exp < 8; exp++) {\n      if (sample <= (0x1F << (exp + 3))) {\n        exponent = exp;\n        break;\n      }\n    }\n    \n    const mantissa = (sample >> (exponent + 3)) & 0x0F;\n    muLawEncodeTable[i] = ~(sign | (exponent << 4) | mantissa);\n  }\n  \n  // Decoding table - Standard µ-law decoding algorithm\n  for (let i = 0; i < 256; i++) {\n    const mulaw = ~i;\n    const exponent = (mulaw & 0x70) >> 4;\n    const mantissa = mulaw & 0x0F;\n\n    let sample;\n    if (exponent === 0) {\n      sample = (mantissa << 4) + 8;\n    } else {\n      sample = ((mantissa + 16) << (exponent + 3)) - 128;\n    }\n\n    // Apply sign\n    if (mulaw & 0x80) {\n      sample = -sample;\n    }\n\n    muLawDecodeTable[i] = sample;\n  }\n}\n\n// Initialize tables on module load\ninitializeMuLawTables();\n\n/**\n * Audio Processor Class\n */\nexport class AudioProcessor {\n  constructor() {\n    this.sampleRate = 8000; // Protocol requirement\n    this.chunkDuration = 20; // 20ms chunks\n    this.chunkSize = (this.sampleRate * this.chunkDuration) / 1000; // 160 samples\n    this.audioBuffer = [];\n  }\n\n  /**\n   * Convert PCM Float32Array to µ-law bytes\n   * @param {Float32Array} pcmData - Input PCM data (-1.0 to 1.0)\n   * @returns {Uint8Array} µ-law encoded data\n   */\n  pcmToMuLaw(pcmData) {\n    const muLawData = new Uint8Array(pcmData.length);\n    \n    for (let i = 0; i < pcmData.length; i++) {\n      // Clamp and scale to 16-bit range\n      let sample = Math.max(-1, Math.min(1, pcmData[i]));\n      sample = Math.round(sample * 32767);\n      \n      // Apply µ-law encoding\n      const sign = sample < 0 ? 0x80 : 0x00;\n      sample = Math.abs(sample);\n      \n      if (sample > MULAW_CLIP) sample = MULAW_CLIP;\n      sample += MULAW_BIAS;\n      \n      let exponent = 7;\n      for (let exp = 0; exp < 8; exp++) {\n        if (sample <= (0x1F << (exp + 3))) {\n          exponent = exp;\n          break;\n        }\n      }\n      \n      const mantissa = (sample >> (exponent + 3)) & 0x0F;\n      muLawData[i] = ~(sign | (exponent << 4) | mantissa);\n    }\n    \n    return muLawData;\n  }\n\n  /**\n   * Convert µ-law bytes to PCM Float32Array\n   * Standard ITU-T G.711 µ-law decoding algorithm\n   * @param {Uint8Array} muLawData - Input µ-law data\n   * @returns {Float32Array} PCM data (-1.0 to 1.0)\n   */\n  muLawToPcm(muLawData) {\n    const pcmData = new Float32Array(muLawData.length);\n\n    for (let i = 0; i < muLawData.length; i++) {\n      const mulaw = muLawData[i];\n\n      // Invert the µ-law value (standard G.711 step)\n      const inverted = ~mulaw;\n\n      // Extract components\n      const sign = (inverted & 0x80) ? -1 : 1;\n      const exponent = (inverted & 0x70) >> 4;\n      const mantissa = inverted & 0x0F;\n\n      // Standard G.711 µ-law decoding\n      let sample;\n      if (exponent === 0) {\n        sample = (mantissa << 4) + 8;\n      } else {\n        sample = ((mantissa + 16) << (exponent + 3)) - 128;\n      }\n\n      // Apply sign and normalize to -1.0 to 1.0 range for Web Audio API\n      pcmData[i] = (sign * sample) / 32768.0;\n    }\n\n    return pcmData;\n  }\n\n  /**\n   * Resample audio data\n   * @param {Float32Array} inputData - Input audio data\n   * @param {number} inputSampleRate - Input sample rate\n   * @param {number} outputSampleRate - Output sample rate\n   * @returns {Float32Array} Resampled audio data\n   */\n  resample(inputData, inputSampleRate, outputSampleRate) {\n    if (inputSampleRate === outputSampleRate) {\n      return inputData;\n    }\n\n    const ratio = inputSampleRate / outputSampleRate;\n    const outputLength = Math.floor(inputData.length / ratio);\n    const outputData = new Float32Array(outputLength);\n\n    for (let i = 0; i < outputLength; i++) {\n      const sourceIndex = i * ratio;\n      const index = Math.floor(sourceIndex);\n      const fraction = sourceIndex - index;\n\n      if (index + 1 < inputData.length) {\n        // Linear interpolation\n        outputData[i] = inputData[index] * (1 - fraction) + inputData[index + 1] * fraction;\n      } else {\n        outputData[i] = inputData[index];\n      }\n    }\n\n    return outputData;\n  }\n\n  /**\n   * Process audio input for transmission\n   * @param {Float32Array} inputData - Raw audio input\n   * @param {number} inputSampleRate - Input sample rate\n   * @returns {Array<Uint8Array>} Array of µ-law encoded chunks\n   */\n  processInputAudio(inputData, inputSampleRate = 44100) {\n    // Resample to 8kHz if needed\n    let processedData = inputData;\n    if (inputSampleRate !== this.sampleRate) {\n      processedData = this.resample(inputData, inputSampleRate, this.sampleRate);\n    }\n\n    // Add to buffer\n    this.audioBuffer.push(...processedData);\n\n    // Extract chunks\n    const chunks = [];\n    while (this.audioBuffer.length >= this.chunkSize) {\n      const chunk = this.audioBuffer.splice(0, this.chunkSize);\n      const chunkArray = new Float32Array(chunk);\n      const muLawChunk = this.pcmToMuLaw(chunkArray);\n      chunks.push(muLawChunk);\n    }\n\n    return chunks;\n  }\n\n  /**\n   * Process received audio for playback\n   * @param {string} base64Data - Base64 encoded µ-law data\n   * @returns {AudioBuffer} Web Audio API AudioBuffer\n   */\n  async processReceivedAudio(base64Data, audioContext) {\n    try {\n      console.log('audioProcessor.processReceivedAudio - Input base64 length:', base64Data.length);\n      \n      // Decode base64 to µ-law data\n      const binaryString = atob(base64Data);\n      console.log('Decoded binary string length:', binaryString.length);\n      \n      const muLawData = new Uint8Array(binaryString.length);\n      for (let i = 0; i < binaryString.length; i++) {\n        muLawData[i] = binaryString.charCodeAt(i);\n      }\n      console.log('µ-law data created, length:', muLawData.length);\n\n      // Convert µ-law to PCM\n      const pcmData = this.muLawToPcm(muLawData);\n      console.log('PCM data converted, length:', pcmData.length, 'sample values:', pcmData.slice(0, 5));\n\n      // Check for valid audio data\n      const maxValue = Math.max(...pcmData);\n      const minValue = Math.min(...pcmData);\n      const rms = Math.sqrt(pcmData.reduce((sum, val) => sum + val * val, 0) / pcmData.length);\n      console.log('PCM data range - max:', maxValue, 'min:', minValue, 'RMS:', rms);\n\n      // Ensure we have valid audio data\n      if (maxValue === 0 && minValue === 0) {\n        console.warn('Audio data appears to be silent (all zeros)');\n      }\n\n      // Create AudioBuffer using AudioContext sample rate, not protocol sample rate\n      console.log('Creating AudioBuffer with AudioContext sample rate:', audioContext.sampleRate, 'vs protocol rate:', this.sampleRate);\n      console.log('PCM data length:', pcmData.length, 'original duration:', pcmData.length / this.sampleRate, 'seconds');\n\n      // Resample PCM data from 8kHz to AudioContext sample rate\n      const targetSampleRate = audioContext.sampleRate;\n      const resampledData = this.resample(pcmData, this.sampleRate, targetSampleRate);\n\n      // Ensure we have a valid length for the audio buffer\n      if (resampledData.length === 0) {\n        console.error('Resampled data is empty');\n        return null;\n      }\n\n      const audioBuffer = audioContext.createBuffer(1, resampledData.length, targetSampleRate);\n      audioBuffer.getChannelData(0).set(resampledData);\n      console.log('AudioBuffer created with resampling - new length:', resampledData.length, 'duration:', audioBuffer.duration, 'seconds');\n\n      return audioBuffer;\n    } catch (error) {\n      console.error('Error processing received audio:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Convert audio data to base64 for transmission\n   * @param {Uint8Array} muLawData - µ-law encoded data\n   * @returns {string} Base64 encoded string\n   */\n  encodeToBase64(muLawData) {\n    let binaryString = '';\n    for (let i = 0; i < muLawData.length; i++) {\n      binaryString += String.fromCharCode(muLawData[i]);\n    }\n    return btoa(binaryString);\n  }\n\n  /**\n   * Calculate audio level (RMS) for visualization\n   * @param {Float32Array} audioData - Audio data\n   * @returns {number} Audio level (0-100)\n   */\n  calculateAudioLevel(audioData) {\n    let sum = 0;\n    for (let i = 0; i < audioData.length; i++) {\n      sum += audioData[i] * audioData[i];\n    }\n    const rms = Math.sqrt(sum / audioData.length);\n    return Math.min(100, rms * 100);\n  }\n\n  /**\n   * Apply noise gate to reduce background noise\n   * @param {Float32Array} audioData - Input audio data\n   * @param {number} threshold - Noise gate threshold (0-1)\n   * @returns {Float32Array} Processed audio data\n   */\n  applyNoiseGate(audioData, threshold = 0.01) {\n    const processedData = new Float32Array(audioData.length);\n    \n    for (let i = 0; i < audioData.length; i++) {\n      const sample = Math.abs(audioData[i]);\n      if (sample > threshold) {\n        processedData[i] = audioData[i];\n      } else {\n        processedData[i] = 0;\n      }\n    }\n    \n    return processedData;\n  }\n\n  /**\n   * Clear internal audio buffer\n   */\n  clearBuffer() {\n    this.audioBuffer = [];\n  }\n\n  /**\n   * Get buffer size\n   * @returns {number} Current buffer size\n   */\n  getBufferSize() {\n    return this.audioBuffer.length;\n  }\n}\n\n/**\n * Audio Queue for managing playback timing\n */\nexport class AudioQueue {\n  constructor(audioContext) {\n    this.audioContext = audioContext;\n    this.queue = [];\n    this.isPlaying = false;\n    this.nextStartTime = 0;\n  }\n\n  /**\n   * Add audio buffer to queue\n   * @param {AudioBuffer} audioBuffer - Audio buffer to queue\n   */\n  enqueue(audioBuffer) {\n    console.log('AudioQueue.enqueue - Adding buffer with duration:', audioBuffer.duration, 'seconds');\n    this.queue.push(audioBuffer);\n    console.log('Queue length now:', this.queue.length, 'isPlaying:', this.isPlaying);\n    if (!this.isPlaying) {\n      this.playNext();\n    }\n  }\n\n  /**\n   * Play next audio buffer in queue\n   */\n  playNext() {\n    if (this.queue.length === 0) {\n      console.log('AudioQueue.playNext - Queue empty, stopping playback');\n      this.isPlaying = false;\n      return;\n    }\n\n    console.log('AudioQueue.playNext - Playing next buffer, queue length:', this.queue.length);\n    this.isPlaying = true;\n    const audioBuffer = this.queue.shift();\n    \n    // Verify AudioContext state\n    console.log('AudioQueue.playNext - AudioContext state:', this.audioContext.state, 'Sample rate:', this.audioContext.sampleRate);\n    \n    const source = this.audioContext.createBufferSource();\n    source.buffer = audioBuffer;\n\n    // Create a gain node for better control\n    const gainNode = this.audioContext.createGain();\n    gainNode.gain.value = 1.0; // Full volume\n\n    // Connect: source -> gain -> destination\n    source.connect(gainNode);\n    gainNode.connect(this.audioContext.destination);\n\n    const currentTime = this.audioContext.currentTime;\n    const startTime = Math.max(currentTime, this.nextStartTime);\n\n    console.log('AudioQueue.playNext - Starting playback at time:', startTime, 'duration:', audioBuffer.duration);\n    console.log('AudioQueue.playNext - Buffer details: channels:', audioBuffer.numberOfChannels, 'length:', audioBuffer.length, 'sampleRate:', audioBuffer.sampleRate);\n\n    // Check if audio data has content\n    const channelData = audioBuffer.getChannelData(0);\n    const maxAmplitude = Math.max(...channelData.slice(0, Math.min(1000, channelData.length)));\n    const minAmplitude = Math.min(...channelData.slice(0, Math.min(1000, channelData.length)));\n    console.log('AudioQueue.playNext - Audio amplitude check - max:', maxAmplitude, 'min:', minAmplitude, 'samples preview:', channelData.slice(0, 5));\n\n    // Ensure AudioContext is running\n    if (this.audioContext.state !== 'running') {\n      console.warn('AudioContext is not running, attempting to resume...');\n      this.audioContext.resume().then(() => {\n        console.log('AudioContext resumed successfully');\n      });\n    }\n\n    source.start(startTime);\n    this.nextStartTime = startTime + audioBuffer.duration;\n\n    source.onended = () => {\n      console.log('AudioQueue.playNext - Audio ended, playing next...');\n      this.playNext();\n    };\n  }\n\n  /**\n   * Clear the audio queue\n   */\n  clear() {\n    this.queue = [];\n    this.isPlaying = false;\n    this.nextStartTime = 0;\n  }\n\n  /**\n   * Test audio playback with a simple tone\n   */\n  testAudioPlayback() {\n    if (!this.audioContext) {\n      console.error('AudioContext not available for test');\n      return;\n    }\n\n    console.log('Testing audio playback with 440Hz tone...');\n\n    // Create a 1-second 440Hz sine wave\n    const sampleRate = this.audioContext.sampleRate;\n    const duration = 1.0; // 1 second\n    const frequency = 440; // A4 note\n    const length = sampleRate * duration;\n\n    const audioBuffer = this.audioContext.createBuffer(1, length, sampleRate);\n    const channelData = audioBuffer.getChannelData(0);\n\n    for (let i = 0; i < length; i++) {\n      channelData[i] = Math.sin(2 * Math.PI * frequency * i / sampleRate) * 0.3;\n    }\n\n    const source = this.audioContext.createBufferSource();\n    source.buffer = audioBuffer;\n    source.connect(this.audioContext.destination);\n    source.start();\n\n    console.log('Test tone started - should hear 440Hz for 1 second');\n  }\n\n  /**\n   * Test µ-law decoding with a simple pattern\n   */\n  testMuLawDecoding() {\n    // Create a simple test pattern\n    const testData = new Uint8Array([0x00, 0x80, 0xFF, 0x7F, 0x40, 0xC0]);\n    const decoded = this.muLawToPcm(testData);\n    console.log('µ-law test - input:', Array.from(testData), 'decoded:', Array.from(decoded));\n    return decoded;\n  }\n}\n\n// Export singleton instance\nexport const audioProcessor = new AudioProcessor();\n\n// Expose for debugging in browser console\nif (typeof window !== 'undefined') {\n  window.audioProcessor = audioProcessor;\n  window.AudioQueue = AudioQueue;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA,MAAMA,UAAU,GAAG,IAAI;AACvB,MAAMC,UAAU,GAAG,KAAK;;AAExB;AACA,MAAMC,gBAAgB,GAAG,IAAIC,KAAK,CAAC,GAAG,CAAC;AACvC,MAAMC,gBAAgB,GAAG,IAAID,KAAK,CAAC,GAAG,CAAC;;AAEvC;AACA,SAASE,qBAAqBA,CAAA,EAAG;EAC/B;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;IAC5B,IAAIC,MAAM,GAAGD,CAAC,GAAG,GAAG;IACpBC,MAAM,GAAGA,MAAM,GAAG,GAAG,CAAC,CAAC;;IAEvB,MAAMC,IAAI,GAAID,MAAM,IAAI,CAAC,GAAI,IAAI;IACjC,IAAIC,IAAI,KAAK,CAAC,EAAED,MAAM,GAAG,CAACA,MAAM;IAChC,IAAIA,MAAM,GAAGN,UAAU,EAAEM,MAAM,GAAGN,UAAU;IAE5CM,MAAM,IAAIP,UAAU;IACpB,IAAIS,QAAQ,GAAG,CAAC;IAChB,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC,EAAEA,GAAG,EAAE,EAAE;MAChC,IAAIH,MAAM,IAAK,IAAI,IAAKG,GAAG,GAAG,CAAG,EAAE;QACjCD,QAAQ,GAAGC,GAAG;QACd;MACF;IACF;IAEA,MAAMC,QAAQ,GAAIJ,MAAM,IAAKE,QAAQ,GAAG,CAAE,GAAI,IAAI;IAClDP,gBAAgB,CAACI,CAAC,CAAC,GAAG,EAAEE,IAAI,GAAIC,QAAQ,IAAI,CAAE,GAAGE,QAAQ,CAAC;EAC5D;;EAEA;EACA,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;IAC5B,MAAMM,KAAK,GAAG,CAACN,CAAC;IAChB,MAAMG,QAAQ,GAAG,CAACG,KAAK,GAAG,IAAI,KAAK,CAAC;IACpC,MAAMD,QAAQ,GAAGC,KAAK,GAAG,IAAI;IAE7B,IAAIL,MAAM;IACV,IAAIE,QAAQ,KAAK,CAAC,EAAE;MAClBF,MAAM,GAAG,CAACI,QAAQ,IAAI,CAAC,IAAI,CAAC;IAC9B,CAAC,MAAM;MACLJ,MAAM,GAAG,CAAEI,QAAQ,GAAG,EAAE,IAAMF,QAAQ,GAAG,CAAE,IAAI,GAAG;IACpD;;IAEA;IACA,IAAIG,KAAK,GAAG,IAAI,EAAE;MAChBL,MAAM,GAAG,CAACA,MAAM;IAClB;IAEAH,gBAAgB,CAACE,CAAC,CAAC,GAAGC,MAAM;EAC9B;AACF;;AAEA;AACAF,qBAAqB,CAAC,CAAC;;AAEvB;AACA;AACA;AACA,OAAO,MAAMQ,cAAc,CAAC;EAC1BC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,UAAU,GAAG,IAAI,CAAC,CAAC;IACxB,IAAI,CAACC,aAAa,GAAG,EAAE,CAAC,CAAC;IACzB,IAAI,CAACC,SAAS,GAAI,IAAI,CAACF,UAAU,GAAG,IAAI,CAACC,aAAa,GAAI,IAAI,CAAC,CAAC;IAChE,IAAI,CAACE,WAAW,GAAG,EAAE;EACvB;;EAEA;AACF;AACA;AACA;AACA;EACEC,UAAUA,CAACC,OAAO,EAAE;IAClB,MAAMC,SAAS,GAAG,IAAIC,UAAU,CAACF,OAAO,CAACG,MAAM,CAAC;IAEhD,KAAK,IAAIjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,OAAO,CAACG,MAAM,EAAEjB,CAAC,EAAE,EAAE;MACvC;MACA,IAAIC,MAAM,GAAGiB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEN,OAAO,CAACd,CAAC,CAAC,CAAC,CAAC;MAClDC,MAAM,GAAGiB,IAAI,CAACG,KAAK,CAACpB,MAAM,GAAG,KAAK,CAAC;;MAEnC;MACA,MAAMC,IAAI,GAAGD,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;MACrCA,MAAM,GAAGiB,IAAI,CAACI,GAAG,CAACrB,MAAM,CAAC;MAEzB,IAAIA,MAAM,GAAGN,UAAU,EAAEM,MAAM,GAAGN,UAAU;MAC5CM,MAAM,IAAIP,UAAU;MAEpB,IAAIS,QAAQ,GAAG,CAAC;MAChB,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC,EAAEA,GAAG,EAAE,EAAE;QAChC,IAAIH,MAAM,IAAK,IAAI,IAAKG,GAAG,GAAG,CAAG,EAAE;UACjCD,QAAQ,GAAGC,GAAG;UACd;QACF;MACF;MAEA,MAAMC,QAAQ,GAAIJ,MAAM,IAAKE,QAAQ,GAAG,CAAE,GAAI,IAAI;MAClDY,SAAS,CAACf,CAAC,CAAC,GAAG,EAAEE,IAAI,GAAIC,QAAQ,IAAI,CAAE,GAAGE,QAAQ,CAAC;IACrD;IAEA,OAAOU,SAAS;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEQ,UAAUA,CAACR,SAAS,EAAE;IACpB,MAAMD,OAAO,GAAG,IAAIU,YAAY,CAACT,SAAS,CAACE,MAAM,CAAC;IAElD,KAAK,IAAIjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,SAAS,CAACE,MAAM,EAAEjB,CAAC,EAAE,EAAE;MACzC,MAAMM,KAAK,GAAGS,SAAS,CAACf,CAAC,CAAC;;MAE1B;MACA,MAAMyB,QAAQ,GAAG,CAACnB,KAAK;;MAEvB;MACA,MAAMJ,IAAI,GAAIuB,QAAQ,GAAG,IAAI,GAAI,CAAC,CAAC,GAAG,CAAC;MACvC,MAAMtB,QAAQ,GAAG,CAACsB,QAAQ,GAAG,IAAI,KAAK,CAAC;MACvC,MAAMpB,QAAQ,GAAGoB,QAAQ,GAAG,IAAI;;MAEhC;MACA,IAAIxB,MAAM;MACV,IAAIE,QAAQ,KAAK,CAAC,EAAE;QAClBF,MAAM,GAAG,CAACI,QAAQ,IAAI,CAAC,IAAI,CAAC;MAC9B,CAAC,MAAM;QACLJ,MAAM,GAAG,CAAEI,QAAQ,GAAG,EAAE,IAAMF,QAAQ,GAAG,CAAE,IAAI,GAAG;MACpD;;MAEA;MACAW,OAAO,CAACd,CAAC,CAAC,GAAIE,IAAI,GAAGD,MAAM,GAAI,OAAO;IACxC;IAEA,OAAOa,OAAO;EAChB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEY,QAAQA,CAACC,SAAS,EAAEC,eAAe,EAAEC,gBAAgB,EAAE;IACrD,IAAID,eAAe,KAAKC,gBAAgB,EAAE;MACxC,OAAOF,SAAS;IAClB;IAEA,MAAMG,KAAK,GAAGF,eAAe,GAAGC,gBAAgB;IAChD,MAAME,YAAY,GAAGb,IAAI,CAACc,KAAK,CAACL,SAAS,CAACV,MAAM,GAAGa,KAAK,CAAC;IACzD,MAAMG,UAAU,GAAG,IAAIT,YAAY,CAACO,YAAY,CAAC;IAEjD,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+B,YAAY,EAAE/B,CAAC,EAAE,EAAE;MACrC,MAAMkC,WAAW,GAAGlC,CAAC,GAAG8B,KAAK;MAC7B,MAAMK,KAAK,GAAGjB,IAAI,CAACc,KAAK,CAACE,WAAW,CAAC;MACrC,MAAME,QAAQ,GAAGF,WAAW,GAAGC,KAAK;MAEpC,IAAIA,KAAK,GAAG,CAAC,GAAGR,SAAS,CAACV,MAAM,EAAE;QAChC;QACAgB,UAAU,CAACjC,CAAC,CAAC,GAAG2B,SAAS,CAACQ,KAAK,CAAC,IAAI,CAAC,GAAGC,QAAQ,CAAC,GAAGT,SAAS,CAACQ,KAAK,GAAG,CAAC,CAAC,GAAGC,QAAQ;MACrF,CAAC,MAAM;QACLH,UAAU,CAACjC,CAAC,CAAC,GAAG2B,SAAS,CAACQ,KAAK,CAAC;MAClC;IACF;IAEA,OAAOF,UAAU;EACnB;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEI,iBAAiBA,CAACV,SAAS,EAAEC,eAAe,GAAG,KAAK,EAAE;IACpD;IACA,IAAIU,aAAa,GAAGX,SAAS;IAC7B,IAAIC,eAAe,KAAK,IAAI,CAACnB,UAAU,EAAE;MACvC6B,aAAa,GAAG,IAAI,CAACZ,QAAQ,CAACC,SAAS,EAAEC,eAAe,EAAE,IAAI,CAACnB,UAAU,CAAC;IAC5E;;IAEA;IACA,IAAI,CAACG,WAAW,CAAC2B,IAAI,CAAC,GAAGD,aAAa,CAAC;;IAEvC;IACA,MAAME,MAAM,GAAG,EAAE;IACjB,OAAO,IAAI,CAAC5B,WAAW,CAACK,MAAM,IAAI,IAAI,CAACN,SAAS,EAAE;MAChD,MAAM8B,KAAK,GAAG,IAAI,CAAC7B,WAAW,CAAC8B,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC/B,SAAS,CAAC;MACxD,MAAMgC,UAAU,GAAG,IAAInB,YAAY,CAACiB,KAAK,CAAC;MAC1C,MAAMG,UAAU,GAAG,IAAI,CAAC/B,UAAU,CAAC8B,UAAU,CAAC;MAC9CH,MAAM,CAACD,IAAI,CAACK,UAAU,CAAC;IACzB;IAEA,OAAOJ,MAAM;EACf;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMK,oBAAoBA,CAACC,UAAU,EAAEC,YAAY,EAAE;IACnD,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,4DAA4D,EAAEH,UAAU,CAAC7B,MAAM,CAAC;;MAE5F;MACA,MAAMiC,YAAY,GAAGC,IAAI,CAACL,UAAU,CAAC;MACrCE,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEC,YAAY,CAACjC,MAAM,CAAC;MAEjE,MAAMF,SAAS,GAAG,IAAIC,UAAU,CAACkC,YAAY,CAACjC,MAAM,CAAC;MACrD,KAAK,IAAIjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkD,YAAY,CAACjC,MAAM,EAAEjB,CAAC,EAAE,EAAE;QAC5Ce,SAAS,CAACf,CAAC,CAAC,GAAGkD,YAAY,CAACE,UAAU,CAACpD,CAAC,CAAC;MAC3C;MACAgD,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAElC,SAAS,CAACE,MAAM,CAAC;;MAE5D;MACA,MAAMH,OAAO,GAAG,IAAI,CAACS,UAAU,CAACR,SAAS,CAAC;MAC1CiC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEnC,OAAO,CAACG,MAAM,EAAE,gBAAgB,EAAEH,OAAO,CAACuC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEjG;MACA,MAAMC,QAAQ,GAAGpC,IAAI,CAACC,GAAG,CAAC,GAAGL,OAAO,CAAC;MACrC,MAAMyC,QAAQ,GAAGrC,IAAI,CAACE,GAAG,CAAC,GAAGN,OAAO,CAAC;MACrC,MAAM0C,GAAG,GAAGtC,IAAI,CAACuC,IAAI,CAAC3C,OAAO,CAAC4C,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,GAAGA,GAAG,EAAE,CAAC,CAAC,GAAG9C,OAAO,CAACG,MAAM,CAAC;MACxF+B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEK,QAAQ,EAAE,MAAM,EAAEC,QAAQ,EAAE,MAAM,EAAEC,GAAG,CAAC;;MAE7E;MACA,IAAIF,QAAQ,KAAK,CAAC,IAAIC,QAAQ,KAAK,CAAC,EAAE;QACpCP,OAAO,CAACa,IAAI,CAAC,6CAA6C,CAAC;MAC7D;;MAEA;MACAb,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEF,YAAY,CAACtC,UAAU,EAAE,mBAAmB,EAAE,IAAI,CAACA,UAAU,CAAC;MACjIuC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEnC,OAAO,CAACG,MAAM,EAAE,oBAAoB,EAAEH,OAAO,CAACG,MAAM,GAAG,IAAI,CAACR,UAAU,EAAE,SAAS,CAAC;;MAElH;MACA,MAAMqD,gBAAgB,GAAGf,YAAY,CAACtC,UAAU;MAChD,MAAMsD,aAAa,GAAG,IAAI,CAACrC,QAAQ,CAACZ,OAAO,EAAE,IAAI,CAACL,UAAU,EAAEqD,gBAAgB,CAAC;;MAE/E;MACA,IAAIC,aAAa,CAAC9C,MAAM,KAAK,CAAC,EAAE;QAC9B+B,OAAO,CAACgB,KAAK,CAAC,yBAAyB,CAAC;QACxC,OAAO,IAAI;MACb;MAEA,MAAMpD,WAAW,GAAGmC,YAAY,CAACkB,YAAY,CAAC,CAAC,EAAEF,aAAa,CAAC9C,MAAM,EAAE6C,gBAAgB,CAAC;MACxFlD,WAAW,CAACsD,cAAc,CAAC,CAAC,CAAC,CAACC,GAAG,CAACJ,aAAa,CAAC;MAChDf,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAEc,aAAa,CAAC9C,MAAM,EAAE,WAAW,EAAEL,WAAW,CAACwD,QAAQ,EAAE,SAAS,CAAC;MAEpI,OAAOxD,WAAW;IACpB,CAAC,CAAC,OAAOoD,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACEK,cAAcA,CAACtD,SAAS,EAAE;IACxB,IAAImC,YAAY,GAAG,EAAE;IACrB,KAAK,IAAIlD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,SAAS,CAACE,MAAM,EAAEjB,CAAC,EAAE,EAAE;MACzCkD,YAAY,IAAIoB,MAAM,CAACC,YAAY,CAACxD,SAAS,CAACf,CAAC,CAAC,CAAC;IACnD;IACA,OAAOwE,IAAI,CAACtB,YAAY,CAAC;EAC3B;;EAEA;AACF;AACA;AACA;AACA;EACEuB,mBAAmBA,CAACC,SAAS,EAAE;IAC7B,IAAIf,GAAG,GAAG,CAAC;IACX,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0E,SAAS,CAACzD,MAAM,EAAEjB,CAAC,EAAE,EAAE;MACzC2D,GAAG,IAAIe,SAAS,CAAC1E,CAAC,CAAC,GAAG0E,SAAS,CAAC1E,CAAC,CAAC;IACpC;IACA,MAAMwD,GAAG,GAAGtC,IAAI,CAACuC,IAAI,CAACE,GAAG,GAAGe,SAAS,CAACzD,MAAM,CAAC;IAC7C,OAAOC,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEoC,GAAG,GAAG,GAAG,CAAC;EACjC;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEmB,cAAcA,CAACD,SAAS,EAAEE,SAAS,GAAG,IAAI,EAAE;IAC1C,MAAMtC,aAAa,GAAG,IAAId,YAAY,CAACkD,SAAS,CAACzD,MAAM,CAAC;IAExD,KAAK,IAAIjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0E,SAAS,CAACzD,MAAM,EAAEjB,CAAC,EAAE,EAAE;MACzC,MAAMC,MAAM,GAAGiB,IAAI,CAACI,GAAG,CAACoD,SAAS,CAAC1E,CAAC,CAAC,CAAC;MACrC,IAAIC,MAAM,GAAG2E,SAAS,EAAE;QACtBtC,aAAa,CAACtC,CAAC,CAAC,GAAG0E,SAAS,CAAC1E,CAAC,CAAC;MACjC,CAAC,MAAM;QACLsC,aAAa,CAACtC,CAAC,CAAC,GAAG,CAAC;MACtB;IACF;IAEA,OAAOsC,aAAa;EACtB;;EAEA;AACF;AACA;EACEuC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACjE,WAAW,GAAG,EAAE;EACvB;;EAEA;AACF;AACA;AACA;EACEkE,aAAaA,CAAA,EAAG;IACd,OAAO,IAAI,CAAClE,WAAW,CAACK,MAAM;EAChC;AACF;;AAEA;AACA;AACA;AACA,OAAO,MAAM8D,UAAU,CAAC;EACtBvE,WAAWA,CAACuC,YAAY,EAAE;IACxB,IAAI,CAACA,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACiC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,aAAa,GAAG,CAAC;EACxB;;EAEA;AACF;AACA;AACA;EACEC,OAAOA,CAACvE,WAAW,EAAE;IACnBoC,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAErC,WAAW,CAACwD,QAAQ,EAAE,SAAS,CAAC;IACjG,IAAI,CAACY,KAAK,CAACzC,IAAI,CAAC3B,WAAW,CAAC;IAC5BoC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC+B,KAAK,CAAC/D,MAAM,EAAE,YAAY,EAAE,IAAI,CAACgE,SAAS,CAAC;IACjF,IAAI,CAAC,IAAI,CAACA,SAAS,EAAE;MACnB,IAAI,CAACG,QAAQ,CAAC,CAAC;IACjB;EACF;;EAEA;AACF;AACA;EACEA,QAAQA,CAAA,EAAG;IACT,IAAI,IAAI,CAACJ,KAAK,CAAC/D,MAAM,KAAK,CAAC,EAAE;MAC3B+B,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;MACnE,IAAI,CAACgC,SAAS,GAAG,KAAK;MACtB;IACF;IAEAjC,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAE,IAAI,CAAC+B,KAAK,CAAC/D,MAAM,CAAC;IAC1F,IAAI,CAACgE,SAAS,GAAG,IAAI;IACrB,MAAMrE,WAAW,GAAG,IAAI,CAACoE,KAAK,CAACK,KAAK,CAAC,CAAC;;IAEtC;IACArC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE,IAAI,CAACF,YAAY,CAACuC,KAAK,EAAE,cAAc,EAAE,IAAI,CAACvC,YAAY,CAACtC,UAAU,CAAC;IAE/H,MAAM8E,MAAM,GAAG,IAAI,CAACxC,YAAY,CAACyC,kBAAkB,CAAC,CAAC;IACrDD,MAAM,CAACE,MAAM,GAAG7E,WAAW;;IAE3B;IACA,MAAM8E,QAAQ,GAAG,IAAI,CAAC3C,YAAY,CAAC4C,UAAU,CAAC,CAAC;IAC/CD,QAAQ,CAACE,IAAI,CAACC,KAAK,GAAG,GAAG,CAAC,CAAC;;IAE3B;IACAN,MAAM,CAACO,OAAO,CAACJ,QAAQ,CAAC;IACxBA,QAAQ,CAACI,OAAO,CAAC,IAAI,CAAC/C,YAAY,CAACgD,WAAW,CAAC;IAE/C,MAAMC,WAAW,GAAG,IAAI,CAACjD,YAAY,CAACiD,WAAW;IACjD,MAAMC,SAAS,GAAG/E,IAAI,CAACC,GAAG,CAAC6E,WAAW,EAAE,IAAI,CAACd,aAAa,CAAC;IAE3DlC,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEgD,SAAS,EAAE,WAAW,EAAErF,WAAW,CAACwD,QAAQ,CAAC;IAC7GpB,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAErC,WAAW,CAACsF,gBAAgB,EAAE,SAAS,EAAEtF,WAAW,CAACK,MAAM,EAAE,aAAa,EAAEL,WAAW,CAACH,UAAU,CAAC;;IAElK;IACA,MAAM0F,WAAW,GAAGvF,WAAW,CAACsD,cAAc,CAAC,CAAC,CAAC;IACjD,MAAMkC,YAAY,GAAGlF,IAAI,CAACC,GAAG,CAAC,GAAGgF,WAAW,CAAC9C,KAAK,CAAC,CAAC,EAAEnC,IAAI,CAACE,GAAG,CAAC,IAAI,EAAE+E,WAAW,CAAClF,MAAM,CAAC,CAAC,CAAC;IAC1F,MAAMoF,YAAY,GAAGnF,IAAI,CAACE,GAAG,CAAC,GAAG+E,WAAW,CAAC9C,KAAK,CAAC,CAAC,EAAEnC,IAAI,CAACE,GAAG,CAAC,IAAI,EAAE+E,WAAW,CAAClF,MAAM,CAAC,CAAC,CAAC;IAC1F+B,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAEmD,YAAY,EAAE,MAAM,EAAEC,YAAY,EAAE,kBAAkB,EAAEF,WAAW,CAAC9C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;IAElJ;IACA,IAAI,IAAI,CAACN,YAAY,CAACuC,KAAK,KAAK,SAAS,EAAE;MACzCtC,OAAO,CAACa,IAAI,CAAC,sDAAsD,CAAC;MACpE,IAAI,CAACd,YAAY,CAACuD,MAAM,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACpCvD,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAClD,CAAC,CAAC;IACJ;IAEAsC,MAAM,CAACiB,KAAK,CAACP,SAAS,CAAC;IACvB,IAAI,CAACf,aAAa,GAAGe,SAAS,GAAGrF,WAAW,CAACwD,QAAQ;IAErDmB,MAAM,CAACkB,OAAO,GAAG,MAAM;MACrBzD,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MACjE,IAAI,CAACmC,QAAQ,CAAC,CAAC;IACjB,CAAC;EACH;;EAEA;AACF;AACA;EACEsB,KAAKA,CAAA,EAAG;IACN,IAAI,CAAC1B,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,aAAa,GAAG,CAAC;EACxB;;EAEA;AACF;AACA;EACEyB,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAAC,IAAI,CAAC5D,YAAY,EAAE;MACtBC,OAAO,CAACgB,KAAK,CAAC,qCAAqC,CAAC;MACpD;IACF;IAEAhB,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;;IAExD;IACA,MAAMxC,UAAU,GAAG,IAAI,CAACsC,YAAY,CAACtC,UAAU;IAC/C,MAAM2D,QAAQ,GAAG,GAAG,CAAC,CAAC;IACtB,MAAMwC,SAAS,GAAG,GAAG,CAAC,CAAC;IACvB,MAAM3F,MAAM,GAAGR,UAAU,GAAG2D,QAAQ;IAEpC,MAAMxD,WAAW,GAAG,IAAI,CAACmC,YAAY,CAACkB,YAAY,CAAC,CAAC,EAAEhD,MAAM,EAAER,UAAU,CAAC;IACzE,MAAM0F,WAAW,GAAGvF,WAAW,CAACsD,cAAc,CAAC,CAAC,CAAC;IAEjD,KAAK,IAAIlE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,MAAM,EAAEjB,CAAC,EAAE,EAAE;MAC/BmG,WAAW,CAACnG,CAAC,CAAC,GAAGkB,IAAI,CAAC2F,GAAG,CAAC,CAAC,GAAG3F,IAAI,CAAC4F,EAAE,GAAGF,SAAS,GAAG5G,CAAC,GAAGS,UAAU,CAAC,GAAG,GAAG;IAC3E;IAEA,MAAM8E,MAAM,GAAG,IAAI,CAACxC,YAAY,CAACyC,kBAAkB,CAAC,CAAC;IACrDD,MAAM,CAACE,MAAM,GAAG7E,WAAW;IAC3B2E,MAAM,CAACO,OAAO,CAAC,IAAI,CAAC/C,YAAY,CAACgD,WAAW,CAAC;IAC7CR,MAAM,CAACiB,KAAK,CAAC,CAAC;IAEdxD,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;EACnE;;EAEA;AACF;AACA;EACE8D,iBAAiBA,CAAA,EAAG;IAClB;IACA,MAAMC,QAAQ,GAAG,IAAIhG,UAAU,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACrE,MAAMiG,OAAO,GAAG,IAAI,CAAC1F,UAAU,CAACyF,QAAQ,CAAC;IACzChE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEpD,KAAK,CAACqH,IAAI,CAACF,QAAQ,CAAC,EAAE,UAAU,EAAEnH,KAAK,CAACqH,IAAI,CAACD,OAAO,CAAC,CAAC;IACzF,OAAOA,OAAO;EAChB;AACF;;AAEA;AACA,OAAO,MAAME,cAAc,GAAG,IAAI5G,cAAc,CAAC,CAAC;;AAElD;AACA,IAAI,OAAO6G,MAAM,KAAK,WAAW,EAAE;EACjCA,MAAM,CAACD,cAAc,GAAGA,cAAc;EACtCC,MAAM,CAACrC,UAAU,GAAGA,UAAU;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}