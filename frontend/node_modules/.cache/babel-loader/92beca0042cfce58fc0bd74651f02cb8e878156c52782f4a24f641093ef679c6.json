{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect, useRef, useCallback } from 'react';\nimport toast from 'react-hot-toast';\nimport { audioProcessor, AudioQueue } from '../utils/audioProcessor';\n\n// WebSocket connection states\nexport const CONNECTION_STATES = {\n  DISCONNECTED: 'disconnected',\n  CONNECTING: 'connecting',\n  CONNECTED: 'connected',\n  ERROR: 'error'\n};\n\n// Call states\nexport const CALL_STATES = {\n  IDLE: 'idle',\n  CONNECTING: 'connecting',\n  IN_CALL: 'in_call',\n  ENDING: 'ending',\n  ENDED: 'ended',\n  ERROR: 'error'\n};\n\n// Event types from the WebSocket protocol\nconst EVENT_TYPES = {\n  CONNECTED: 'connected',\n  START: 'start',\n  MEDIA: 'media',\n  STOP: 'stop',\n  DTMF: 'dtmf',\n  MARK: 'mark',\n  CLEAR: 'clear'\n};\n\n// User management event types\nconst USER_EVENT_TYPES = {\n  USER_REGISTER: 'user_register',\n  CALL_START: 'call_start',\n  CALL_END: 'call_end'\n};\nexport const useVoiceSocket = (wsUrl = 'ws://localhost:5010') => {\n  _s();\n  const [connectionState, setConnectionState] = useState(CONNECTION_STATES.DISCONNECTED);\n  const [callState, setCallState] = useState(CALL_STATES.IDLE);\n  const [streamId, setStreamId] = useState(null);\n  const [sessionId, setSessionId] = useState(null);\n  const [sequenceNumber, setSequenceNumber] = useState(0);\n  const wsRef = useRef(null);\n  const reconnectTimeoutRef = useRef(null);\n  const reconnectAttempts = useRef(0);\n  const isConnectingRef = useRef(false);\n  const maxReconnectAttempts = 5;\n  const reconnectDelay = 3000;\n\n  // Audio processing refs\n  const audioContextRef = useRef(null);\n  const audioQueueRef = useRef(null);\n\n  // Generate unique session ID\n  const generateSessionId = useCallback(() => {\n    return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();\n  }, []);\n\n  // Initialize audio context (only after user interaction)\n  const initializeAudioContext = useCallback(async () => {\n    try {\n      if (!audioContextRef.current) {\n        // Create AudioContext only after user interaction\n        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();\n        audioQueueRef.current = new AudioQueue(audioContextRef.current);\n        console.log('AudioContext created successfully - Sample rate:', audioContextRef.current.sampleRate, 'State:', audioContextRef.current.state);\n      }\n      if (audioContextRef.current.state === 'suspended') {\n        await audioContextRef.current.resume();\n        console.log('AudioContext resumed successfully - State:', audioContextRef.current.state);\n      }\n      console.log('AudioContext final state:', audioContextRef.current.state, 'Sample rate:', audioContextRef.current.sampleRate);\n    } catch (error) {\n      console.error('Failed to initialize audio context:', error);\n      toast.error('Failed to initialize audio system');\n      throw error;\n    }\n  }, []);\n\n  // Convert base64 to audio buffer and play\n  const playAudioFromBase64 = useCallback(async base64Data => {\n    try {\n      console.log('playAudioFromBase64 called with data length:', base64Data ? base64Data.length : 'null');\n      if (!audioContextRef.current || !audioQueueRef.current) {\n        var _audioContextRef$curr;\n        console.log('Initializing audio context...');\n        await initializeAudioContext();\n        console.log('Audio context initialized:', (_audioContextRef$curr = audioContextRef.current) === null || _audioContextRef$curr === void 0 ? void 0 : _audioContextRef$curr.state);\n      }\n      console.log('Processing audio with audioProcessor...');\n      // Use the improved audio processor\n      const audioBuffer = await audioProcessor.processReceivedAudio(base64Data, audioContextRef.current);\n      console.log('AudioBuffer created:', audioBuffer ? 'success' : 'failed', audioBuffer === null || audioBuffer === void 0 ? void 0 : audioBuffer.duration);\n      if (audioBuffer && audioQueueRef.current) {\n        console.log('Enqueueing audio buffer for playback...');\n        audioQueueRef.current.enqueue(audioBuffer);\n        console.log('Audio enqueued successfully');\n      } else {\n        console.warn('Failed to enqueue audio - missing audioBuffer or audioQueue');\n      }\n    } catch (error) {\n      console.error('Error playing audio:', error);\n    }\n  }, [initializeAudioContext]);\n\n  // Handle incoming WebSocket messages\n  const handleMessage = useCallback(async event => {\n    try {\n      const data = JSON.parse(event.data);\n\n      // Handle protocol events\n      if (data.event) {\n        switch (data.event) {\n          case EVENT_TYPES.CONNECTED:\n            console.log('WebSocket protocol connected');\n            setConnectionState(CONNECTION_STATES.CONNECTED);\n            break;\n          case EVENT_TYPES.START:\n            console.log('Call started:', data);\n            setStreamId(data.streamSid);\n            setCallState(CALL_STATES.IN_CALL);\n            toast.success('Call connected!');\n            break;\n          case EVENT_TYPES.MEDIA:\n            // Handle incoming audio data\n            console.log('MEDIA event received:', data);\n            if (data.media && data.media.payload) {\n              console.log('Playing audio from MEDIA event, payload length:', data.media.payload.length);\n              try {\n                await playAudioFromBase64(data.media.payload);\n                console.log('Audio playback completed successfully');\n              } catch (error) {\n                console.error('Error playing MEDIA audio:', error);\n              }\n            } else {\n              console.warn('MEDIA event received but no payload found:', data);\n            }\n            break;\n          case EVENT_TYPES.STOP:\n            console.log('Call stopped:', data);\n            setCallState(CALL_STATES.ENDED);\n            setStreamId(null);\n            toast.success('Call ended');\n            break;\n          case EVENT_TYPES.MARK:\n            console.log('Mark event received:', data);\n            break;\n          case EVENT_TYPES.CLEAR:\n            console.log('Clear event received:', data);\n            // Clear audio queue and processor buffer\n            if (audioQueueRef.current) {\n              audioQueueRef.current.clear();\n            }\n            audioProcessor.clearBuffer();\n            break;\n          default:\n            console.log('Unknown protocol event:', data.event);\n        }\n      }\n\n      // Handle user management events\n      else if (data.type) {\n        switch (data.type) {\n          case 'user_registered':\n            console.log('User registered successfully - setting connection state to CONNECTED');\n            setConnectionState(CONNECTION_STATES.CONNECTED);\n            console.log('Connection state updated to CONNECTED');\n            toast.success('Connected to server');\n            break;\n          case 'error':\n            console.error('Server error:', data.message);\n            toast.error(data.message || 'Server error occurred');\n            setConnectionState(CONNECTION_STATES.ERROR);\n            setCallState(CALL_STATES.ERROR);\n            break;\n          default:\n            console.log('Unknown user event:', data.type);\n        }\n      }\n    } catch (error) {\n      console.error('Error handling WebSocket message:', error);\n    }\n  }, [playAudioFromBase64]);\n\n  // Connect to WebSocket\n  const connect = useCallback(async () => {\n    var _wsRef$current, _wsRef$current2;\n    // Prevent multiple simultaneous connection attempts\n    if (((_wsRef$current = wsRef.current) === null || _wsRef$current === void 0 ? void 0 : _wsRef$current.readyState) === WebSocket.OPEN || ((_wsRef$current2 = wsRef.current) === null || _wsRef$current2 === void 0 ? void 0 : _wsRef$current2.readyState) === WebSocket.CONNECTING || isConnectingRef.current) {\n      console.log('WebSocket already connected or connecting, skipping connection attempt');\n      return;\n    }\n    try {\n      isConnectingRef.current = true;\n      setConnectionState(CONNECTION_STATES.CONNECTING);\n      console.log(`Attempting to connect to ${wsUrl}`);\n      const ws = new WebSocket(wsUrl);\n      wsRef.current = ws;\n      ws.onopen = async () => {\n        console.log('WebSocket connected');\n        // Keep connection state as CONNECTING until user registration is confirmed\n        reconnectAttempts.current = 0;\n        isConnectingRef.current = false;\n\n        // Don't initialize audio context here - wait for user interaction\n        // This prevents the \"AudioContext was not allowed to start\" error\n\n        // Generate session ID and register user\n        const newSessionId = generateSessionId();\n        setSessionId(newSessionId);\n\n        // Register user\n        const registerMessage = {\n          type: USER_EVENT_TYPES.USER_REGISTER,\n          sessionId: newSessionId,\n          userData: {\n            name: 'Web User',\n            mobile: '+1234567890',\n            userId: 'web_user_' + Date.now(),\n            sessionType: 'call',\n            target: 'english_tutor' // Default target for web users\n          }\n        };\n        ws.send(JSON.stringify(registerMessage));\n      };\n      ws.onmessage = handleMessage;\n      ws.onclose = event => {\n        console.log('WebSocket closed:', event.code, event.reason);\n        isConnectingRef.current = false;\n        setConnectionState(CONNECTION_STATES.DISCONNECTED);\n        setCallState(CALL_STATES.IDLE);\n\n        // Attempt to reconnect if not a normal closure and not too many attempts\n        if (event.code !== 1000 && reconnectAttempts.current < maxReconnectAttempts) {\n          reconnectAttempts.current++;\n          console.log(`Attempting to reconnect (${reconnectAttempts.current}/${maxReconnectAttempts})...`);\n          reconnectTimeoutRef.current = setTimeout(() => {\n            connect();\n          }, reconnectDelay);\n        } else if (reconnectAttempts.current >= maxReconnectAttempts) {\n          console.log('Max reconnection attempts reached');\n          toast.error('Unable to connect to server. Please check if the server is running.');\n        }\n      };\n      ws.onerror = error => {\n        var _wsRef$current3;\n        console.error('WebSocket error:', error);\n        isConnectingRef.current = false;\n        setConnectionState(CONNECTION_STATES.ERROR);\n\n        // More specific error handling\n        if (error.type === 'error' && ((_wsRef$current3 = wsRef.current) === null || _wsRef$current3 === void 0 ? void 0 : _wsRef$current3.readyState) === WebSocket.CLOSED) {\n          toast.error('Failed to connect to server. Please check if the WebSocket server is running on port 5010.');\n        } else {\n          toast.error('Connection error occurred');\n        }\n      };\n    } catch (error) {\n      console.error('Failed to connect:', error);\n      isConnectingRef.current = false;\n      setConnectionState(CONNECTION_STATES.ERROR);\n      toast.error('Failed to connect to server');\n    }\n  }, [wsUrl, handleMessage, generateSessionId]);\n\n  // Disconnect from WebSocket\n  const disconnect = useCallback(() => {\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n      reconnectTimeoutRef.current = null;\n    }\n    isConnectingRef.current = false;\n    if (wsRef.current) {\n      wsRef.current.close(1000, 'User disconnected');\n      wsRef.current = null;\n    }\n    setConnectionState(CONNECTION_STATES.DISCONNECTED);\n    setCallState(CALL_STATES.IDLE);\n    setStreamId(null);\n    setSessionId(null);\n  }, []);\n\n  // Start a call\n  const startCall = useCallback(async () => {\n    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN || !sessionId) {\n      toast.error('Not connected to server');\n      return;\n    }\n    try {\n      setCallState(CALL_STATES.CONNECTING);\n\n      // Initialize audio context on user interaction (call start)\n      // This ensures AudioContext is created after user gesture\n      try {\n        await initializeAudioContext();\n        console.log('Audio context initialized for call');\n\n        // Test audio playback capability\n        if (audioContextRef.current) {\n          console.log('AudioContext state:', audioContextRef.current.state);\n          console.log('AudioContext sample rate:', audioContextRef.current.sampleRate);\n          console.log('AudioContext destination:', audioContextRef.current.destination);\n        }\n      } catch (audioError) {\n        console.error('Failed to initialize audio context:', audioError);\n        toast.error('Failed to initialize audio. Please try again.');\n        setCallState(CALL_STATES.IDLE);\n        return;\n      }\n      const startMessage = {\n        type: USER_EVENT_TYPES.CALL_START,\n        sessionId: sessionId\n      };\n      wsRef.current.send(JSON.stringify(startMessage));\n    } catch (error) {\n      console.error('Error starting call:', error);\n      setCallState(CALL_STATES.IDLE);\n      toast.error('Failed to start call');\n    }\n  }, [sessionId, initializeAudioContext]);\n\n  // End a call\n  const endCall = useCallback(() => {\n    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN || !sessionId) {\n      return;\n    }\n    setCallState(CALL_STATES.ENDING);\n    const endMessage = {\n      type: USER_EVENT_TYPES.CALL_END,\n      sessionId: sessionId,\n      reason: 'user_ended'\n    };\n    wsRef.current.send(JSON.stringify(endMessage));\n  }, [sessionId]);\n\n  // Send audio data\n  const sendAudioData = useCallback(audioData => {\n    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN || !streamId) {\n      return;\n    }\n    try {\n      // Use the improved audio processor to encode\n      const base64Data = audioProcessor.encodeToBase64(new Uint8Array(audioData));\n      const mediaMessage = {\n        event: EVENT_TYPES.MEDIA,\n        streamSid: streamId,\n        sequenceNumber: (sequenceNumber + 1).toString(),\n        media: {\n          chunk: Math.floor(Date.now() / 20).toString(),\n          // 20ms chunks\n          timestamp: Date.now().toString(),\n          payload: base64Data\n        }\n      };\n      wsRef.current.send(JSON.stringify(mediaMessage));\n      setSequenceNumber(prev => prev + 1);\n    } catch (error) {\n      console.error('Error sending audio data:', error);\n    }\n  }, [streamId, sequenceNumber]);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      disconnect();\n      if (audioContextRef.current) {\n        audioContextRef.current.close();\n      }\n    };\n  }, [disconnect]);\n  return {\n    connectionState,\n    callState,\n    streamId,\n    sessionId,\n    connect,\n    disconnect,\n    startCall,\n    endCall,\n    sendAudioData,\n    isConnected: connectionState === CONNECTION_STATES.CONNECTED,\n    isInCall: callState === CALL_STATES.IN_CALL,\n    isConnecting: callState === CALL_STATES.CONNECTING\n  };\n};\n_s(useVoiceSocket, \"IAxehSd+Ci43mEWDAySzrIqn+dU=\");", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "useCallback", "toast", "audioProcessor", "AudioQueue", "CONNECTION_STATES", "DISCONNECTED", "CONNECTING", "CONNECTED", "ERROR", "CALL_STATES", "IDLE", "IN_CALL", "ENDING", "ENDED", "EVENT_TYPES", "START", "MEDIA", "STOP", "DTMF", "MARK", "CLEAR", "USER_EVENT_TYPES", "USER_REGISTER", "CALL_START", "CALL_END", "useVoiceSocket", "wsUrl", "_s", "connectionState", "setConnectionState", "callState", "setCallState", "streamId", "setStreamId", "sessionId", "setSessionId", "sequenceNumber", "setSequenceNumber", "wsRef", "reconnectTimeoutRef", "reconnectAttempts", "isConnectingRef", "maxReconnectAttempts", "reconnectDelay", "audioContextRef", "audioQueueRef", "generateSessionId", "Math", "random", "toString", "substr", "Date", "now", "initializeAudioContext", "current", "window", "AudioContext", "webkitAudioContext", "console", "log", "sampleRate", "state", "resume", "error", "playAudioFromBase64", "base64Data", "length", "_audioContextRef$curr", "audioBuffer", "processReceivedAudio", "duration", "enqueue", "warn", "handleMessage", "event", "data", "JSON", "parse", "streamSid", "success", "media", "payload", "clear", "<PERSON><PERSON><PERSON><PERSON>", "type", "message", "connect", "_wsRef$current", "_wsRef$current2", "readyState", "WebSocket", "OPEN", "ws", "onopen", "newSessionId", "registerMessage", "userData", "name", "mobile", "userId", "sessionType", "target", "send", "stringify", "onmessage", "onclose", "code", "reason", "setTimeout", "onerror", "_wsRef$current3", "CLOSED", "disconnect", "clearTimeout", "close", "startCall", "destination", "audioError", "startMessage", "endCall", "endMessage", "sendAudioData", "audioData", "encodeToBase64", "Uint8Array", "mediaMessage", "chunk", "floor", "timestamp", "prev", "isConnected", "isInCall", "isConnecting"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/hooks/useVoiceSocket.js"], "sourcesContent": ["import { useState, useEffect, useRef, useCallback } from 'react';\nimport toast from 'react-hot-toast';\nimport { audioProcessor, AudioQueue } from '../utils/audioProcessor';\n\n// WebSocket connection states\nexport const CONNECTION_STATES = {\n  DISCONNECTED: 'disconnected',\n  CONNECTING: 'connecting',\n  CONNECTED: 'connected',\n  ERROR: 'error'\n};\n\n// Call states\nexport const CALL_STATES = {\n  IDLE: 'idle',\n  CONNECTING: 'connecting',\n  IN_CALL: 'in_call',\n  ENDING: 'ending',\n  ENDED: 'ended',\n  ERROR: 'error'\n};\n\n// Event types from the WebSocket protocol\nconst EVENT_TYPES = {\n  CONNECTED: 'connected',\n  START: 'start',\n  MEDIA: 'media',\n  STOP: 'stop',\n  DTMF: 'dtmf',\n  MARK: 'mark',\n  CLEAR: 'clear'\n};\n\n// User management event types\nconst USER_EVENT_TYPES = {\n  USER_REGISTER: 'user_register',\n  CALL_START: 'call_start',\n  CALL_END: 'call_end'\n};\n\nexport const useVoiceSocket = (wsUrl = 'ws://localhost:5010') => {\n  const [connectionState, setConnectionState] = useState(CONNECTION_STATES.DISCONNECTED);\n  const [callState, setCallState] = useState(CALL_STATES.IDLE);\n  const [streamId, setStreamId] = useState(null);\n  const [sessionId, setSessionId] = useState(null);\n  const [sequenceNumber, setSequenceNumber] = useState(0);\n  \n  const wsRef = useRef(null);\n  const reconnectTimeoutRef = useRef(null);\n  const reconnectAttempts = useRef(0);\n  const isConnectingRef = useRef(false);\n  const maxReconnectAttempts = 5;\n  const reconnectDelay = 3000;\n\n  // Audio processing refs\n  const audioContextRef = useRef(null);\n  const audioQueueRef = useRef(null);\n\n  // Generate unique session ID\n  const generateSessionId = useCallback(() => {\n    return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();\n  }, []);\n\n  // Initialize audio context (only after user interaction)\n  const initializeAudioContext = useCallback(async () => {\n    try {\n      if (!audioContextRef.current) {\n        // Create AudioContext only after user interaction\n        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();\n        audioQueueRef.current = new AudioQueue(audioContextRef.current);\n        console.log('AudioContext created successfully - Sample rate:', audioContextRef.current.sampleRate, 'State:', audioContextRef.current.state);\n      }\n\n      if (audioContextRef.current.state === 'suspended') {\n        await audioContextRef.current.resume();\n        console.log('AudioContext resumed successfully - State:', audioContextRef.current.state);\n      }\n      \n      console.log('AudioContext final state:', audioContextRef.current.state, 'Sample rate:', audioContextRef.current.sampleRate);\n    } catch (error) {\n      console.error('Failed to initialize audio context:', error);\n      toast.error('Failed to initialize audio system');\n      throw error;\n    }\n  }, []);\n\n  // Convert base64 to audio buffer and play\n  const playAudioFromBase64 = useCallback(async (base64Data) => {\n    try {\n      console.log('playAudioFromBase64 called with data length:', base64Data ? base64Data.length : 'null');\n      \n      if (!audioContextRef.current || !audioQueueRef.current) {\n        console.log('Initializing audio context...');\n        await initializeAudioContext();\n        console.log('Audio context initialized:', audioContextRef.current?.state);\n      }\n\n      console.log('Processing audio with audioProcessor...');\n      // Use the improved audio processor\n      const audioBuffer = await audioProcessor.processReceivedAudio(base64Data, audioContextRef.current);\n      console.log('AudioBuffer created:', audioBuffer ? 'success' : 'failed', audioBuffer?.duration);\n\n      if (audioBuffer && audioQueueRef.current) {\n        console.log('Enqueueing audio buffer for playback...');\n        audioQueueRef.current.enqueue(audioBuffer);\n        console.log('Audio enqueued successfully');\n      } else {\n        console.warn('Failed to enqueue audio - missing audioBuffer or audioQueue');\n      }\n\n    } catch (error) {\n      console.error('Error playing audio:', error);\n    }\n  }, [initializeAudioContext]);\n\n  // Handle incoming WebSocket messages\n  const handleMessage = useCallback(async (event) => {\n    try {\n      const data = JSON.parse(event.data);\n\n      // Handle protocol events\n      if (data.event) {\n        switch (data.event) {\n          case EVENT_TYPES.CONNECTED:\n            console.log('WebSocket protocol connected');\n            setConnectionState(CONNECTION_STATES.CONNECTED);\n            break;\n\n          case EVENT_TYPES.START:\n            console.log('Call started:', data);\n            setStreamId(data.streamSid);\n            setCallState(CALL_STATES.IN_CALL);\n            toast.success('Call connected!');\n            break;\n\n          case EVENT_TYPES.MEDIA:\n            // Handle incoming audio data\n            console.log('MEDIA event received:', data);\n            if (data.media && data.media.payload) {\n              console.log('Playing audio from MEDIA event, payload length:', data.media.payload.length);\n              try {\n                await playAudioFromBase64(data.media.payload);\n                console.log('Audio playback completed successfully');\n              } catch (error) {\n                console.error('Error playing MEDIA audio:', error);\n              }\n            } else {\n              console.warn('MEDIA event received but no payload found:', data);\n            }\n            break;\n\n          case EVENT_TYPES.STOP:\n            console.log('Call stopped:', data);\n            setCallState(CALL_STATES.ENDED);\n            setStreamId(null);\n            toast.success('Call ended');\n            break;\n\n          case EVENT_TYPES.MARK:\n            console.log('Mark event received:', data);\n            break;\n\n          case EVENT_TYPES.CLEAR:\n            console.log('Clear event received:', data);\n            // Clear audio queue and processor buffer\n            if (audioQueueRef.current) {\n              audioQueueRef.current.clear();\n            }\n            audioProcessor.clearBuffer();\n            break;\n\n          default:\n            console.log('Unknown protocol event:', data.event);\n        }\n      }\n\n      // Handle user management events\n      else if (data.type) {\n        switch (data.type) {\n          case 'user_registered':\n            console.log('User registered successfully - setting connection state to CONNECTED');\n            setConnectionState(CONNECTION_STATES.CONNECTED);\n            console.log('Connection state updated to CONNECTED');\n            toast.success('Connected to server');\n            break;\n\n          case 'error':\n            console.error('Server error:', data.message);\n            toast.error(data.message || 'Server error occurred');\n            setConnectionState(CONNECTION_STATES.ERROR);\n            setCallState(CALL_STATES.ERROR);\n            break;\n\n          default:\n            console.log('Unknown user event:', data.type);\n        }\n      }\n\n    } catch (error) {\n      console.error('Error handling WebSocket message:', error);\n    }\n  }, [playAudioFromBase64]);\n\n  // Connect to WebSocket\n  const connect = useCallback(async () => {\n    // Prevent multiple simultaneous connection attempts\n    if (wsRef.current?.readyState === WebSocket.OPEN ||\n        wsRef.current?.readyState === WebSocket.CONNECTING ||\n        isConnectingRef.current) {\n      console.log('WebSocket already connected or connecting, skipping connection attempt');\n      return;\n    }\n\n    try {\n      isConnectingRef.current = true;\n      setConnectionState(CONNECTION_STATES.CONNECTING);\n      console.log(`Attempting to connect to ${wsUrl}`);\n\n      const ws = new WebSocket(wsUrl);\n      wsRef.current = ws;\n\n      ws.onopen = async () => {\n        console.log('WebSocket connected');\n        // Keep connection state as CONNECTING until user registration is confirmed\n        reconnectAttempts.current = 0;\n        isConnectingRef.current = false;\n\n        // Don't initialize audio context here - wait for user interaction\n        // This prevents the \"AudioContext was not allowed to start\" error\n\n        // Generate session ID and register user\n        const newSessionId = generateSessionId();\n        setSessionId(newSessionId);\n\n        // Register user\n        const registerMessage = {\n          type: USER_EVENT_TYPES.USER_REGISTER,\n          sessionId: newSessionId,\n          userData: {\n            name: 'Web User',\n            mobile: '+1234567890',\n            userId: 'web_user_' + Date.now(),\n            sessionType: 'call',\n            target: 'english_tutor'  // Default target for web users\n          }\n        };\n\n        ws.send(JSON.stringify(registerMessage));\n      };\n\n      ws.onmessage = handleMessage;\n\n      ws.onclose = (event) => {\n        console.log('WebSocket closed:', event.code, event.reason);\n        isConnectingRef.current = false;\n        setConnectionState(CONNECTION_STATES.DISCONNECTED);\n        setCallState(CALL_STATES.IDLE);\n\n        // Attempt to reconnect if not a normal closure and not too many attempts\n        if (event.code !== 1000 && reconnectAttempts.current < maxReconnectAttempts) {\n          reconnectAttempts.current++;\n          console.log(`Attempting to reconnect (${reconnectAttempts.current}/${maxReconnectAttempts})...`);\n\n          reconnectTimeoutRef.current = setTimeout(() => {\n            connect();\n          }, reconnectDelay);\n        } else if (reconnectAttempts.current >= maxReconnectAttempts) {\n          console.log('Max reconnection attempts reached');\n          toast.error('Unable to connect to server. Please check if the server is running.');\n        }\n      };\n\n      ws.onerror = (error) => {\n        console.error('WebSocket error:', error);\n        isConnectingRef.current = false;\n        setConnectionState(CONNECTION_STATES.ERROR);\n\n        // More specific error handling\n        if (error.type === 'error' && wsRef.current?.readyState === WebSocket.CLOSED) {\n          toast.error('Failed to connect to server. Please check if the WebSocket server is running on port 5010.');\n        } else {\n          toast.error('Connection error occurred');\n        }\n      };\n\n    } catch (error) {\n      console.error('Failed to connect:', error);\n      isConnectingRef.current = false;\n      setConnectionState(CONNECTION_STATES.ERROR);\n      toast.error('Failed to connect to server');\n    }\n  }, [wsUrl, handleMessage, generateSessionId]);\n\n  // Disconnect from WebSocket\n  const disconnect = useCallback(() => {\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n      reconnectTimeoutRef.current = null;\n    }\n\n    isConnectingRef.current = false;\n\n    if (wsRef.current) {\n      wsRef.current.close(1000, 'User disconnected');\n      wsRef.current = null;\n    }\n\n    setConnectionState(CONNECTION_STATES.DISCONNECTED);\n    setCallState(CALL_STATES.IDLE);\n    setStreamId(null);\n    setSessionId(null);\n  }, []);\n\n  // Start a call\n  const startCall = useCallback(async () => {\n    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN || !sessionId) {\n      toast.error('Not connected to server');\n      return;\n    }\n\n    try {\n      setCallState(CALL_STATES.CONNECTING);\n\n      // Initialize audio context on user interaction (call start)\n      // This ensures AudioContext is created after user gesture\n      try {\n        await initializeAudioContext();\n        console.log('Audio context initialized for call');\n\n        // Test audio playback capability\n        if (audioContextRef.current) {\n          console.log('AudioContext state:', audioContextRef.current.state);\n          console.log('AudioContext sample rate:', audioContextRef.current.sampleRate);\n          console.log('AudioContext destination:', audioContextRef.current.destination);\n        }\n      } catch (audioError) {\n        console.error('Failed to initialize audio context:', audioError);\n        toast.error('Failed to initialize audio. Please try again.');\n        setCallState(CALL_STATES.IDLE);\n        return;\n      }\n\n      const startMessage = {\n        type: USER_EVENT_TYPES.CALL_START,\n        sessionId: sessionId\n      };\n\n      wsRef.current.send(JSON.stringify(startMessage));\n    } catch (error) {\n      console.error('Error starting call:', error);\n      setCallState(CALL_STATES.IDLE);\n      toast.error('Failed to start call');\n    }\n  }, [sessionId, initializeAudioContext]);\n\n  // End a call\n  const endCall = useCallback(() => {\n    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN || !sessionId) {\n      return;\n    }\n\n    setCallState(CALL_STATES.ENDING);\n    \n    const endMessage = {\n      type: USER_EVENT_TYPES.CALL_END,\n      sessionId: sessionId,\n      reason: 'user_ended'\n    };\n    \n    wsRef.current.send(JSON.stringify(endMessage));\n  }, [sessionId]);\n\n  // Send audio data\n  const sendAudioData = useCallback((audioData) => {\n    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN || !streamId) {\n      return;\n    }\n\n    try {\n      // Use the improved audio processor to encode\n      const base64Data = audioProcessor.encodeToBase64(new Uint8Array(audioData));\n\n      const mediaMessage = {\n        event: EVENT_TYPES.MEDIA,\n        streamSid: streamId,\n        sequenceNumber: (sequenceNumber + 1).toString(),\n        media: {\n          chunk: Math.floor(Date.now() / 20).toString(), // 20ms chunks\n          timestamp: Date.now().toString(),\n          payload: base64Data\n        }\n      };\n\n      wsRef.current.send(JSON.stringify(mediaMessage));\n      setSequenceNumber(prev => prev + 1);\n\n    } catch (error) {\n      console.error('Error sending audio data:', error);\n    }\n  }, [streamId, sequenceNumber]);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      disconnect();\n      if (audioContextRef.current) {\n        audioContextRef.current.close();\n      }\n    };\n  }, [disconnect]);\n\n  return {\n    connectionState,\n    callState,\n    streamId,\n    sessionId,\n    connect,\n    disconnect,\n    startCall,\n    endCall,\n    sendAudioData,\n    isConnected: connectionState === CONNECTION_STATES.CONNECTED,\n    isInCall: callState === CALL_STATES.IN_CALL,\n    isConnecting: callState === CALL_STATES.CONNECTING\n  };\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAChE,OAAOC,KAAK,MAAM,iBAAiB;AACnC,SAASC,cAAc,EAAEC,UAAU,QAAQ,yBAAyB;;AAEpE;AACA,OAAO,MAAMC,iBAAiB,GAAG;EAC/BC,YAAY,EAAE,cAAc;EAC5BC,UAAU,EAAE,YAAY;EACxBC,SAAS,EAAE,WAAW;EACtBC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,OAAO,MAAMC,WAAW,GAAG;EACzBC,IAAI,EAAE,MAAM;EACZJ,UAAU,EAAE,YAAY;EACxBK,OAAO,EAAE,SAAS;EAClBC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,OAAO;EACdL,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMM,WAAW,GAAG;EAClBP,SAAS,EAAE,WAAW;EACtBQ,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,gBAAgB,GAAG;EACvBC,aAAa,EAAE,eAAe;EAC9BC,UAAU,EAAE,YAAY;EACxBC,QAAQ,EAAE;AACZ,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,GAAG,qBAAqB,KAAK;EAAAC,EAAA;EAC/D,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAACO,iBAAiB,CAACC,YAAY,CAAC;EACtF,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAACY,WAAW,CAACC,IAAI,CAAC;EAC5D,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACqC,SAAS,EAAEC,YAAY,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACuC,cAAc,EAAEC,iBAAiB,CAAC,GAAGxC,QAAQ,CAAC,CAAC,CAAC;EAEvD,MAAMyC,KAAK,GAAGvC,MAAM,CAAC,IAAI,CAAC;EAC1B,MAAMwC,mBAAmB,GAAGxC,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMyC,iBAAiB,GAAGzC,MAAM,CAAC,CAAC,CAAC;EACnC,MAAM0C,eAAe,GAAG1C,MAAM,CAAC,KAAK,CAAC;EACrC,MAAM2C,oBAAoB,GAAG,CAAC;EAC9B,MAAMC,cAAc,GAAG,IAAI;;EAE3B;EACA,MAAMC,eAAe,GAAG7C,MAAM,CAAC,IAAI,CAAC;EACpC,MAAM8C,aAAa,GAAG9C,MAAM,CAAC,IAAI,CAAC;;EAElC;EACA,MAAM+C,iBAAiB,GAAG9C,WAAW,CAAC,MAAM;IAC1C,OAAO,UAAU,GAAG+C,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;EAChF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,sBAAsB,GAAGrD,WAAW,CAAC,YAAY;IACrD,IAAI;MACF,IAAI,CAAC4C,eAAe,CAACU,OAAO,EAAE;QAC5B;QACAV,eAAe,CAACU,OAAO,GAAG,KAAKC,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC;QAClFZ,aAAa,CAACS,OAAO,GAAG,IAAInD,UAAU,CAACyC,eAAe,CAACU,OAAO,CAAC;QAC/DI,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEf,eAAe,CAACU,OAAO,CAACM,UAAU,EAAE,QAAQ,EAAEhB,eAAe,CAACU,OAAO,CAACO,KAAK,CAAC;MAC9I;MAEA,IAAIjB,eAAe,CAACU,OAAO,CAACO,KAAK,KAAK,WAAW,EAAE;QACjD,MAAMjB,eAAe,CAACU,OAAO,CAACQ,MAAM,CAAC,CAAC;QACtCJ,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEf,eAAe,CAACU,OAAO,CAACO,KAAK,CAAC;MAC1F;MAEAH,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEf,eAAe,CAACU,OAAO,CAACO,KAAK,EAAE,cAAc,EAAEjB,eAAe,CAACU,OAAO,CAACM,UAAU,CAAC;IAC7H,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D9D,KAAK,CAAC8D,KAAK,CAAC,mCAAmC,CAAC;MAChD,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,mBAAmB,GAAGhE,WAAW,CAAC,MAAOiE,UAAU,IAAK;IAC5D,IAAI;MACFP,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEM,UAAU,GAAGA,UAAU,CAACC,MAAM,GAAG,MAAM,CAAC;MAEpG,IAAI,CAACtB,eAAe,CAACU,OAAO,IAAI,CAACT,aAAa,CAACS,OAAO,EAAE;QAAA,IAAAa,qBAAA;QACtDT,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5C,MAAMN,sBAAsB,CAAC,CAAC;QAC9BK,OAAO,CAACC,GAAG,CAAC,4BAA4B,GAAAQ,qBAAA,GAAEvB,eAAe,CAACU,OAAO,cAAAa,qBAAA,uBAAvBA,qBAAA,CAAyBN,KAAK,CAAC;MAC3E;MAEAH,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD;MACA,MAAMS,WAAW,GAAG,MAAMlE,cAAc,CAACmE,oBAAoB,CAACJ,UAAU,EAAErB,eAAe,CAACU,OAAO,CAAC;MAClGI,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAES,WAAW,GAAG,SAAS,GAAG,QAAQ,EAAEA,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEE,QAAQ,CAAC;MAE9F,IAAIF,WAAW,IAAIvB,aAAa,CAACS,OAAO,EAAE;QACxCI,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;QACtDd,aAAa,CAACS,OAAO,CAACiB,OAAO,CAACH,WAAW,CAAC;QAC1CV,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC5C,CAAC,MAAM;QACLD,OAAO,CAACc,IAAI,CAAC,6DAA6D,CAAC;MAC7E;IAEF,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF,CAAC,EAAE,CAACV,sBAAsB,CAAC,CAAC;;EAE5B;EACA,MAAMoB,aAAa,GAAGzE,WAAW,CAAC,MAAO0E,KAAK,IAAK;IACjD,IAAI;MACF,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC;;MAEnC;MACA,IAAIA,IAAI,CAACD,KAAK,EAAE;QACd,QAAQC,IAAI,CAACD,KAAK;UAChB,KAAK5D,WAAW,CAACP,SAAS;YACxBmD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;YAC3C9B,kBAAkB,CAACzB,iBAAiB,CAACG,SAAS,CAAC;YAC/C;UAEF,KAAKO,WAAW,CAACC,KAAK;YACpB2C,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEgB,IAAI,CAAC;YAClC1C,WAAW,CAAC0C,IAAI,CAACG,SAAS,CAAC;YAC3B/C,YAAY,CAACtB,WAAW,CAACE,OAAO,CAAC;YACjCV,KAAK,CAAC8E,OAAO,CAAC,iBAAiB,CAAC;YAChC;UAEF,KAAKjE,WAAW,CAACE,KAAK;YACpB;YACA0C,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEgB,IAAI,CAAC;YAC1C,IAAIA,IAAI,CAACK,KAAK,IAAIL,IAAI,CAACK,KAAK,CAACC,OAAO,EAAE;cACpCvB,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEgB,IAAI,CAACK,KAAK,CAACC,OAAO,CAACf,MAAM,CAAC;cACzF,IAAI;gBACF,MAAMF,mBAAmB,CAACW,IAAI,CAACK,KAAK,CAACC,OAAO,CAAC;gBAC7CvB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;cACtD,CAAC,CAAC,OAAOI,KAAK,EAAE;gBACdL,OAAO,CAACK,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;cACpD;YACF,CAAC,MAAM;cACLL,OAAO,CAACc,IAAI,CAAC,4CAA4C,EAAEG,IAAI,CAAC;YAClE;YACA;UAEF,KAAK7D,WAAW,CAACG,IAAI;YACnByC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEgB,IAAI,CAAC;YAClC5C,YAAY,CAACtB,WAAW,CAACI,KAAK,CAAC;YAC/BoB,WAAW,CAAC,IAAI,CAAC;YACjBhC,KAAK,CAAC8E,OAAO,CAAC,YAAY,CAAC;YAC3B;UAEF,KAAKjE,WAAW,CAACK,IAAI;YACnBuC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEgB,IAAI,CAAC;YACzC;UAEF,KAAK7D,WAAW,CAACM,KAAK;YACpBsC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEgB,IAAI,CAAC;YAC1C;YACA,IAAI9B,aAAa,CAACS,OAAO,EAAE;cACzBT,aAAa,CAACS,OAAO,CAAC4B,KAAK,CAAC,CAAC;YAC/B;YACAhF,cAAc,CAACiF,WAAW,CAAC,CAAC;YAC5B;UAEF;YACEzB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEgB,IAAI,CAACD,KAAK,CAAC;QACtD;MACF;;MAEA;MAAA,KACK,IAAIC,IAAI,CAACS,IAAI,EAAE;QAClB,QAAQT,IAAI,CAACS,IAAI;UACf,KAAK,iBAAiB;YACpB1B,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAC;YACnF9B,kBAAkB,CAACzB,iBAAiB,CAACG,SAAS,CAAC;YAC/CmD,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;YACpD1D,KAAK,CAAC8E,OAAO,CAAC,qBAAqB,CAAC;YACpC;UAEF,KAAK,OAAO;YACVrB,OAAO,CAACK,KAAK,CAAC,eAAe,EAAEY,IAAI,CAACU,OAAO,CAAC;YAC5CpF,KAAK,CAAC8D,KAAK,CAACY,IAAI,CAACU,OAAO,IAAI,uBAAuB,CAAC;YACpDxD,kBAAkB,CAACzB,iBAAiB,CAACI,KAAK,CAAC;YAC3CuB,YAAY,CAACtB,WAAW,CAACD,KAAK,CAAC;YAC/B;UAEF;YACEkD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEgB,IAAI,CAACS,IAAI,CAAC;QACjD;MACF;IAEF,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAC3D;EACF,CAAC,EAAE,CAACC,mBAAmB,CAAC,CAAC;;EAEzB;EACA,MAAMsB,OAAO,GAAGtF,WAAW,CAAC,YAAY;IAAA,IAAAuF,cAAA,EAAAC,eAAA;IACtC;IACA,IAAI,EAAAD,cAAA,GAAAjD,KAAK,CAACgB,OAAO,cAAAiC,cAAA,uBAAbA,cAAA,CAAeE,UAAU,MAAKC,SAAS,CAACC,IAAI,IAC5C,EAAAH,eAAA,GAAAlD,KAAK,CAACgB,OAAO,cAAAkC,eAAA,uBAAbA,eAAA,CAAeC,UAAU,MAAKC,SAAS,CAACpF,UAAU,IAClDmC,eAAe,CAACa,OAAO,EAAE;MAC3BI,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;MACrF;IACF;IAEA,IAAI;MACFlB,eAAe,CAACa,OAAO,GAAG,IAAI;MAC9BzB,kBAAkB,CAACzB,iBAAiB,CAACE,UAAU,CAAC;MAChDoD,OAAO,CAACC,GAAG,CAAC,4BAA4BjC,KAAK,EAAE,CAAC;MAEhD,MAAMkE,EAAE,GAAG,IAAIF,SAAS,CAAChE,KAAK,CAAC;MAC/BY,KAAK,CAACgB,OAAO,GAAGsC,EAAE;MAElBA,EAAE,CAACC,MAAM,GAAG,YAAY;QACtBnC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;QAClC;QACAnB,iBAAiB,CAACc,OAAO,GAAG,CAAC;QAC7Bb,eAAe,CAACa,OAAO,GAAG,KAAK;;QAE/B;QACA;;QAEA;QACA,MAAMwC,YAAY,GAAGhD,iBAAiB,CAAC,CAAC;QACxCX,YAAY,CAAC2D,YAAY,CAAC;;QAE1B;QACA,MAAMC,eAAe,GAAG;UACtBX,IAAI,EAAE/D,gBAAgB,CAACC,aAAa;UACpCY,SAAS,EAAE4D,YAAY;UACvBE,QAAQ,EAAE;YACRC,IAAI,EAAE,UAAU;YAChBC,MAAM,EAAE,aAAa;YACrBC,MAAM,EAAE,WAAW,GAAGhD,IAAI,CAACC,GAAG,CAAC,CAAC;YAChCgD,WAAW,EAAE,MAAM;YACnBC,MAAM,EAAE,eAAe,CAAE;UAC3B;QACF,CAAC;QAEDT,EAAE,CAACU,IAAI,CAAC1B,IAAI,CAAC2B,SAAS,CAACR,eAAe,CAAC,CAAC;MAC1C,CAAC;MAEDH,EAAE,CAACY,SAAS,GAAG/B,aAAa;MAE5BmB,EAAE,CAACa,OAAO,GAAI/B,KAAK,IAAK;QACtBhB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEe,KAAK,CAACgC,IAAI,EAAEhC,KAAK,CAACiC,MAAM,CAAC;QAC1DlE,eAAe,CAACa,OAAO,GAAG,KAAK;QAC/BzB,kBAAkB,CAACzB,iBAAiB,CAACC,YAAY,CAAC;QAClD0B,YAAY,CAACtB,WAAW,CAACC,IAAI,CAAC;;QAE9B;QACA,IAAIgE,KAAK,CAACgC,IAAI,KAAK,IAAI,IAAIlE,iBAAiB,CAACc,OAAO,GAAGZ,oBAAoB,EAAE;UAC3EF,iBAAiB,CAACc,OAAO,EAAE;UAC3BI,OAAO,CAACC,GAAG,CAAC,4BAA4BnB,iBAAiB,CAACc,OAAO,IAAIZ,oBAAoB,MAAM,CAAC;UAEhGH,mBAAmB,CAACe,OAAO,GAAGsD,UAAU,CAAC,MAAM;YAC7CtB,OAAO,CAAC,CAAC;UACX,CAAC,EAAE3C,cAAc,CAAC;QACpB,CAAC,MAAM,IAAIH,iBAAiB,CAACc,OAAO,IAAIZ,oBAAoB,EAAE;UAC5DgB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;UAChD1D,KAAK,CAAC8D,KAAK,CAAC,qEAAqE,CAAC;QACpF;MACF,CAAC;MAED6B,EAAE,CAACiB,OAAO,GAAI9C,KAAK,IAAK;QAAA,IAAA+C,eAAA;QACtBpD,OAAO,CAACK,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;QACxCtB,eAAe,CAACa,OAAO,GAAG,KAAK;QAC/BzB,kBAAkB,CAACzB,iBAAiB,CAACI,KAAK,CAAC;;QAE3C;QACA,IAAIuD,KAAK,CAACqB,IAAI,KAAK,OAAO,IAAI,EAAA0B,eAAA,GAAAxE,KAAK,CAACgB,OAAO,cAAAwD,eAAA,uBAAbA,eAAA,CAAerB,UAAU,MAAKC,SAAS,CAACqB,MAAM,EAAE;UAC5E9G,KAAK,CAAC8D,KAAK,CAAC,4FAA4F,CAAC;QAC3G,CAAC,MAAM;UACL9D,KAAK,CAAC8D,KAAK,CAAC,2BAA2B,CAAC;QAC1C;MACF,CAAC;IAEH,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CtB,eAAe,CAACa,OAAO,GAAG,KAAK;MAC/BzB,kBAAkB,CAACzB,iBAAiB,CAACI,KAAK,CAAC;MAC3CP,KAAK,CAAC8D,KAAK,CAAC,6BAA6B,CAAC;IAC5C;EACF,CAAC,EAAE,CAACrC,KAAK,EAAE+C,aAAa,EAAE3B,iBAAiB,CAAC,CAAC;;EAE7C;EACA,MAAMkE,UAAU,GAAGhH,WAAW,CAAC,MAAM;IACnC,IAAIuC,mBAAmB,CAACe,OAAO,EAAE;MAC/B2D,YAAY,CAAC1E,mBAAmB,CAACe,OAAO,CAAC;MACzCf,mBAAmB,CAACe,OAAO,GAAG,IAAI;IACpC;IAEAb,eAAe,CAACa,OAAO,GAAG,KAAK;IAE/B,IAAIhB,KAAK,CAACgB,OAAO,EAAE;MACjBhB,KAAK,CAACgB,OAAO,CAAC4D,KAAK,CAAC,IAAI,EAAE,mBAAmB,CAAC;MAC9C5E,KAAK,CAACgB,OAAO,GAAG,IAAI;IACtB;IAEAzB,kBAAkB,CAACzB,iBAAiB,CAACC,YAAY,CAAC;IAClD0B,YAAY,CAACtB,WAAW,CAACC,IAAI,CAAC;IAC9BuB,WAAW,CAAC,IAAI,CAAC;IACjBE,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMgF,SAAS,GAAGnH,WAAW,CAAC,YAAY;IACxC,IAAI,CAACsC,KAAK,CAACgB,OAAO,IAAIhB,KAAK,CAACgB,OAAO,CAACmC,UAAU,KAAKC,SAAS,CAACC,IAAI,IAAI,CAACzD,SAAS,EAAE;MAC/EjC,KAAK,CAAC8D,KAAK,CAAC,yBAAyB,CAAC;MACtC;IACF;IAEA,IAAI;MACFhC,YAAY,CAACtB,WAAW,CAACH,UAAU,CAAC;;MAEpC;MACA;MACA,IAAI;QACF,MAAM+C,sBAAsB,CAAC,CAAC;QAC9BK,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;;QAEjD;QACA,IAAIf,eAAe,CAACU,OAAO,EAAE;UAC3BI,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEf,eAAe,CAACU,OAAO,CAACO,KAAK,CAAC;UACjEH,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEf,eAAe,CAACU,OAAO,CAACM,UAAU,CAAC;UAC5EF,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEf,eAAe,CAACU,OAAO,CAAC8D,WAAW,CAAC;QAC/E;MACF,CAAC,CAAC,OAAOC,UAAU,EAAE;QACnB3D,OAAO,CAACK,KAAK,CAAC,qCAAqC,EAAEsD,UAAU,CAAC;QAChEpH,KAAK,CAAC8D,KAAK,CAAC,+CAA+C,CAAC;QAC5DhC,YAAY,CAACtB,WAAW,CAACC,IAAI,CAAC;QAC9B;MACF;MAEA,MAAM4G,YAAY,GAAG;QACnBlC,IAAI,EAAE/D,gBAAgB,CAACE,UAAU;QACjCW,SAAS,EAAEA;MACb,CAAC;MAEDI,KAAK,CAACgB,OAAO,CAACgD,IAAI,CAAC1B,IAAI,CAAC2B,SAAS,CAACe,YAAY,CAAC,CAAC;IAClD,CAAC,CAAC,OAAOvD,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5ChC,YAAY,CAACtB,WAAW,CAACC,IAAI,CAAC;MAC9BT,KAAK,CAAC8D,KAAK,CAAC,sBAAsB,CAAC;IACrC;EACF,CAAC,EAAE,CAAC7B,SAAS,EAAEmB,sBAAsB,CAAC,CAAC;;EAEvC;EACA,MAAMkE,OAAO,GAAGvH,WAAW,CAAC,MAAM;IAChC,IAAI,CAACsC,KAAK,CAACgB,OAAO,IAAIhB,KAAK,CAACgB,OAAO,CAACmC,UAAU,KAAKC,SAAS,CAACC,IAAI,IAAI,CAACzD,SAAS,EAAE;MAC/E;IACF;IAEAH,YAAY,CAACtB,WAAW,CAACG,MAAM,CAAC;IAEhC,MAAM4G,UAAU,GAAG;MACjBpC,IAAI,EAAE/D,gBAAgB,CAACG,QAAQ;MAC/BU,SAAS,EAAEA,SAAS;MACpByE,MAAM,EAAE;IACV,CAAC;IAEDrE,KAAK,CAACgB,OAAO,CAACgD,IAAI,CAAC1B,IAAI,CAAC2B,SAAS,CAACiB,UAAU,CAAC,CAAC;EAChD,CAAC,EAAE,CAACtF,SAAS,CAAC,CAAC;;EAEf;EACA,MAAMuF,aAAa,GAAGzH,WAAW,CAAE0H,SAAS,IAAK;IAC/C,IAAI,CAACpF,KAAK,CAACgB,OAAO,IAAIhB,KAAK,CAACgB,OAAO,CAACmC,UAAU,KAAKC,SAAS,CAACC,IAAI,IAAI,CAAC3D,QAAQ,EAAE;MAC9E;IACF;IAEA,IAAI;MACF;MACA,MAAMiC,UAAU,GAAG/D,cAAc,CAACyH,cAAc,CAAC,IAAIC,UAAU,CAACF,SAAS,CAAC,CAAC;MAE3E,MAAMG,YAAY,GAAG;QACnBnD,KAAK,EAAE5D,WAAW,CAACE,KAAK;QACxB8D,SAAS,EAAE9C,QAAQ;QACnBI,cAAc,EAAE,CAACA,cAAc,GAAG,CAAC,EAAEa,QAAQ,CAAC,CAAC;QAC/C+B,KAAK,EAAE;UACL8C,KAAK,EAAE/E,IAAI,CAACgF,KAAK,CAAC5E,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAACH,QAAQ,CAAC,CAAC;UAAE;UAC/C+E,SAAS,EAAE7E,IAAI,CAACC,GAAG,CAAC,CAAC,CAACH,QAAQ,CAAC,CAAC;UAChCgC,OAAO,EAAEhB;QACX;MACF,CAAC;MAED3B,KAAK,CAACgB,OAAO,CAACgD,IAAI,CAAC1B,IAAI,CAAC2B,SAAS,CAACsB,YAAY,CAAC,CAAC;MAChDxF,iBAAiB,CAAC4F,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAErC,CAAC,CAAC,OAAOlE,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;EACF,CAAC,EAAE,CAAC/B,QAAQ,EAAEI,cAAc,CAAC,CAAC;;EAE9B;EACAtC,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXkH,UAAU,CAAC,CAAC;MACZ,IAAIpE,eAAe,CAACU,OAAO,EAAE;QAC3BV,eAAe,CAACU,OAAO,CAAC4D,KAAK,CAAC,CAAC;MACjC;IACF,CAAC;EACH,CAAC,EAAE,CAACF,UAAU,CAAC,CAAC;EAEhB,OAAO;IACLpF,eAAe;IACfE,SAAS;IACTE,QAAQ;IACRE,SAAS;IACToD,OAAO;IACP0B,UAAU;IACVG,SAAS;IACTI,OAAO;IACPE,aAAa;IACbS,WAAW,EAAEtG,eAAe,KAAKxB,iBAAiB,CAACG,SAAS;IAC5D4H,QAAQ,EAAErG,SAAS,KAAKrB,WAAW,CAACE,OAAO;IAC3CyH,YAAY,EAAEtG,SAAS,KAAKrB,WAAW,CAACH;EAC1C,CAAC;AACH,CAAC;AAACqB,EAAA,CAjYWF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}