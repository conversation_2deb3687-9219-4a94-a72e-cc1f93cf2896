{"ast": null, "code": "/**\n * Audio Processing Utilities for WebSocket Voice Protocol\n * Handles µ-law encoding/decoding, resampling, and audio format conversions\n */\n\n// µ-law decoding table that matches <PERSON>'s audioop.ulaw2lin()\n// This is a pre-computed lookup table for faster decoding\nconst muLawDecodeTable = new Int16Array([-32124, -31100, -30076, -29052, -28028, -27004, -25980, -24956, -23932, -22908, -21884, -20860, -19836, -18812, -17788, -16764, -15996, -15484, -14972, -14460, -13948, -13436, -12924, -12412, -11900, -11388, -10876, -10364, -9852, -9340, -8828, -8316, -7932, -7676, -7420, -7164, -6908, -6652, -6396, -6140, -5884, -5628, -5372, -5116, -4860, -4604, -4348, -4092, -3900, -3772, -3644, -3516, -3388, -3260, -3132, -3004, -2876, -2748, -2620, -2492, -2364, -2236, -2108, -1980, -1884, -1820, -1756, -1692, -1628, -1564, -1500, -1436, -1372, -1308, -1244, -1180, -1116, -1052, -988, -924, -876, -844, -812, -780, -748, -716, -684, -652, -620, -588, -556, -524, -492, -460, -428, -396, -372, -356, -340, -324, -308, -292, -276, -260, -244, -228, -212, -196, -180, -164, -148, -132, -120, -112, -104, -96, -88, -80, -72, -64, -56, -48, -40, -32, -24, -16, -8, 0, 32124, 31100, 30076, 29052, 28028, 27004, 25980, 24956, 23932, 22908, 21884, 20860, 19836, 18812, 17788, 16764, 15996, 15484, 14972, 14460, 13948, 13436, 12924, 12412, 11900, 11388, 10876, 10364, 9852, 9340, 8828, 8316, 7932, 7676, 7420, 7164, 6908, 6652, 6396, 6140, 5884, 5628, 5372, 5116, 4860, 4604, 4348, 4092, 3900, 3772, 3644, 3516, 3388, 3260, 3132, 3004, 2876, 2748, 2620, 2492, 2364, 2236, 2108, 1980, 1884, 1820, 1756, 1692, 1628, 1564, 1500, 1436, 1372, 1308, 1244, 1180, 1116, 1052, 988, 924, 876, 844, 812, 780, 748, 716, 684, 652, 620, 588, 556, 524, 492, 460, 428, 396, 372, 356, 340, 324, 308, 292, 276, 260, 244, 228, 212, 196, 180, 164, 148, 132, 120, 112, 104, 96, 88, 80, 72, 64, 56, 48, 40, 32, 24, 16, 8, 0]);\n\n/**\n * Audio Processor Class\n */\nexport class AudioProcessor {\n  constructor() {\n    this.sampleRate = 8000; // Protocol requirement\n    this.chunkDuration = 20; // 20ms chunks\n    this.chunkSize = this.sampleRate * this.chunkDuration / 1000; // 160 samples\n    this.audioBuffer = [];\n  }\n\n  /**\n   * Convert PCM Float32Array to µ-law bytes\n   * @param {Float32Array} pcmData - Input PCM data (-1.0 to 1.0)\n   * @returns {Uint8Array} µ-law encoded data\n   */\n  pcmToMuLaw(pcmData) {\n    const muLawData = new Uint8Array(pcmData.length);\n    for (let i = 0; i < pcmData.length; i++) {\n      // Clamp and scale to 16-bit range\n      let sample = Math.max(-1, Math.min(1, pcmData[i]));\n      sample = Math.round(sample * 32767);\n\n      // Apply µ-law encoding\n      const sign = sample < 0 ? 0x80 : 0x00;\n      sample = Math.abs(sample);\n      if (sample > MULAW_CLIP) sample = MULAW_CLIP;\n      sample += MULAW_BIAS;\n      let exponent = 7;\n      for (let exp = 0; exp < 8; exp++) {\n        if (sample <= 0x1F << exp + 3) {\n          exponent = exp;\n          break;\n        }\n      }\n      const mantissa = sample >> exponent + 3 & 0x0F;\n      muLawData[i] = ~(sign | exponent << 4 | mantissa);\n    }\n    return muLawData;\n  }\n\n  /**\n   * Convert µ-law bytes to PCM Float32Array\n   * Uses lookup table that matches Python's audioop.ulaw2lin()\n   * @param {Uint8Array} muLawData - Input µ-law data\n   * @returns {Float32Array} PCM data (-1.0 to 1.0)\n   */\n  muLawToPcm(muLawData) {\n    const pcmData = new Float32Array(muLawData.length);\n    for (let i = 0; i < muLawData.length; i++) {\n      // Use lookup table for exact compatibility with Python audioop\n      const sample = muLawDecodeTable[muLawData[i]];\n\n      // Normalize to -1.0 to 1.0 range for Web Audio API\n      pcmData[i] = sample / 32768.0;\n    }\n    return pcmData;\n  }\n\n  /**\n   * Resample audio data\n   * @param {Float32Array} inputData - Input audio data\n   * @param {number} inputSampleRate - Input sample rate\n   * @param {number} outputSampleRate - Output sample rate\n   * @returns {Float32Array} Resampled audio data\n   */\n  resample(inputData, inputSampleRate, outputSampleRate) {\n    if (inputSampleRate === outputSampleRate) {\n      return inputData;\n    }\n    const ratio = inputSampleRate / outputSampleRate;\n    const outputLength = Math.floor(inputData.length / ratio);\n    const outputData = new Float32Array(outputLength);\n    for (let i = 0; i < outputLength; i++) {\n      const sourceIndex = i * ratio;\n      const index = Math.floor(sourceIndex);\n      const fraction = sourceIndex - index;\n      if (index + 1 < inputData.length) {\n        // Linear interpolation\n        outputData[i] = inputData[index] * (1 - fraction) + inputData[index + 1] * fraction;\n      } else {\n        outputData[i] = inputData[index];\n      }\n    }\n    return outputData;\n  }\n\n  /**\n   * Process audio input for transmission\n   * @param {Float32Array} inputData - Raw audio input\n   * @param {number} inputSampleRate - Input sample rate\n   * @returns {Array<Uint8Array>} Array of µ-law encoded chunks\n   */\n  processInputAudio(inputData, inputSampleRate = 44100) {\n    // Resample to 8kHz if needed\n    let processedData = inputData;\n    if (inputSampleRate !== this.sampleRate) {\n      processedData = this.resample(inputData, inputSampleRate, this.sampleRate);\n    }\n\n    // Add to buffer\n    this.audioBuffer.push(...processedData);\n\n    // Extract chunks\n    const chunks = [];\n    while (this.audioBuffer.length >= this.chunkSize) {\n      const chunk = this.audioBuffer.splice(0, this.chunkSize);\n      const chunkArray = new Float32Array(chunk);\n      const muLawChunk = this.pcmToMuLaw(chunkArray);\n      chunks.push(muLawChunk);\n    }\n    return chunks;\n  }\n\n  /**\n   * Process received audio for playback\n   * @param {string} base64Data - Base64 encoded µ-law data\n   * @returns {AudioBuffer} Web Audio API AudioBuffer\n   */\n  async processReceivedAudio(base64Data, audioContext) {\n    try {\n      console.log('audioProcessor.processReceivedAudio - Input base64 length:', base64Data.length);\n\n      // Decode base64 to µ-law data\n      const binaryString = atob(base64Data);\n      console.log('Decoded binary string length:', binaryString.length);\n      const muLawData = new Uint8Array(binaryString.length);\n      for (let i = 0; i < binaryString.length; i++) {\n        muLawData[i] = binaryString.charCodeAt(i);\n      }\n      console.log('µ-law data created, length:', muLawData.length);\n\n      // Convert µ-law to PCM\n      const pcmData = this.muLawToPcm(muLawData);\n      console.log('PCM data converted, length:', pcmData.length, 'sample values:', pcmData.slice(0, 5));\n\n      // Check for valid audio data\n      const maxValue = Math.max(...pcmData);\n      const minValue = Math.min(...pcmData);\n      const rms = Math.sqrt(pcmData.reduce((sum, val) => sum + val * val, 0) / pcmData.length);\n      console.log('PCM data range - max:', maxValue, 'min:', minValue, 'RMS:', rms);\n\n      // Ensure we have valid audio data\n      if (maxValue === 0 && minValue === 0) {\n        console.warn('Audio data appears to be silent (all zeros)');\n      }\n\n      // Create AudioBuffer using AudioContext sample rate, not protocol sample rate\n      console.log('Creating AudioBuffer with AudioContext sample rate:', audioContext.sampleRate, 'vs protocol rate:', this.sampleRate);\n      console.log('PCM data length:', pcmData.length, 'original duration:', pcmData.length / this.sampleRate, 'seconds');\n\n      // Resample PCM data from 8kHz to AudioContext sample rate\n      const targetSampleRate = audioContext.sampleRate;\n      const resampledData = this.resample(pcmData, this.sampleRate, targetSampleRate);\n\n      // Ensure we have a valid length for the audio buffer\n      if (resampledData.length === 0) {\n        console.error('Resampled data is empty');\n        return null;\n      }\n      const audioBuffer = audioContext.createBuffer(1, resampledData.length, targetSampleRate);\n      audioBuffer.getChannelData(0).set(resampledData);\n      console.log('AudioBuffer created with resampling - new length:', resampledData.length, 'duration:', audioBuffer.duration, 'seconds');\n      return audioBuffer;\n    } catch (error) {\n      console.error('Error processing received audio:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Convert audio data to base64 for transmission\n   * @param {Uint8Array} muLawData - µ-law encoded data\n   * @returns {string} Base64 encoded string\n   */\n  encodeToBase64(muLawData) {\n    let binaryString = '';\n    for (let i = 0; i < muLawData.length; i++) {\n      binaryString += String.fromCharCode(muLawData[i]);\n    }\n    return btoa(binaryString);\n  }\n\n  /**\n   * Calculate audio level (RMS) for visualization\n   * @param {Float32Array} audioData - Audio data\n   * @returns {number} Audio level (0-100)\n   */\n  calculateAudioLevel(audioData) {\n    let sum = 0;\n    for (let i = 0; i < audioData.length; i++) {\n      sum += audioData[i] * audioData[i];\n    }\n    const rms = Math.sqrt(sum / audioData.length);\n    return Math.min(100, rms * 100);\n  }\n\n  /**\n   * Apply noise gate to reduce background noise\n   * @param {Float32Array} audioData - Input audio data\n   * @param {number} threshold - Noise gate threshold (0-1)\n   * @returns {Float32Array} Processed audio data\n   */\n  applyNoiseGate(audioData, threshold = 0.01) {\n    const processedData = new Float32Array(audioData.length);\n    for (let i = 0; i < audioData.length; i++) {\n      const sample = Math.abs(audioData[i]);\n      if (sample > threshold) {\n        processedData[i] = audioData[i];\n      } else {\n        processedData[i] = 0;\n      }\n    }\n    return processedData;\n  }\n\n  /**\n   * Clear internal audio buffer\n   */\n  clearBuffer() {\n    this.audioBuffer = [];\n  }\n\n  /**\n   * Get buffer size\n   * @returns {number} Current buffer size\n   */\n  getBufferSize() {\n    return this.audioBuffer.length;\n  }\n}\n\n/**\n * Audio Queue for managing playback timing\n */\nexport class AudioQueue {\n  constructor(audioContext) {\n    this.audioContext = audioContext;\n    this.queue = [];\n    this.isPlaying = false;\n    this.nextStartTime = 0;\n  }\n\n  /**\n   * Add audio buffer to queue\n   * @param {AudioBuffer} audioBuffer - Audio buffer to queue\n   */\n  enqueue(audioBuffer) {\n    console.log('AudioQueue.enqueue - Adding buffer with duration:', audioBuffer.duration, 'seconds');\n    this.queue.push(audioBuffer);\n    console.log('Queue length now:', this.queue.length, 'isPlaying:', this.isPlaying);\n    if (!this.isPlaying) {\n      this.playNext();\n    }\n  }\n\n  /**\n   * Play next audio buffer in queue\n   */\n  playNext() {\n    if (this.queue.length === 0) {\n      console.log('AudioQueue.playNext - Queue empty, stopping playback');\n      this.isPlaying = false;\n      return;\n    }\n    console.log('AudioQueue.playNext - Playing next buffer, queue length:', this.queue.length);\n    this.isPlaying = true;\n    const audioBuffer = this.queue.shift();\n\n    // Verify AudioContext state\n    console.log('AudioQueue.playNext - AudioContext state:', this.audioContext.state, 'Sample rate:', this.audioContext.sampleRate);\n    const source = this.audioContext.createBufferSource();\n    source.buffer = audioBuffer;\n\n    // Create a gain node for better control\n    const gainNode = this.audioContext.createGain();\n    gainNode.gain.value = 1.0; // Full volume\n\n    // Connect: source -> gain -> destination\n    source.connect(gainNode);\n    gainNode.connect(this.audioContext.destination);\n    const currentTime = this.audioContext.currentTime;\n    const startTime = Math.max(currentTime, this.nextStartTime);\n    console.log('AudioQueue.playNext - Starting playback at time:', startTime, 'duration:', audioBuffer.duration);\n    console.log('AudioQueue.playNext - Buffer details: channels:', audioBuffer.numberOfChannels, 'length:', audioBuffer.length, 'sampleRate:', audioBuffer.sampleRate);\n\n    // Check if audio data has content\n    const channelData = audioBuffer.getChannelData(0);\n    const maxAmplitude = Math.max(...channelData.slice(0, Math.min(1000, channelData.length)));\n    const minAmplitude = Math.min(...channelData.slice(0, Math.min(1000, channelData.length)));\n    console.log('AudioQueue.playNext - Audio amplitude check - max:', maxAmplitude, 'min:', minAmplitude, 'samples preview:', channelData.slice(0, 5));\n\n    // Ensure AudioContext is running\n    if (this.audioContext.state !== 'running') {\n      console.warn('AudioContext is not running, attempting to resume...');\n      this.audioContext.resume().then(() => {\n        console.log('AudioContext resumed successfully');\n      });\n    }\n    source.start(startTime);\n    this.nextStartTime = startTime + audioBuffer.duration;\n    source.onended = () => {\n      console.log('AudioQueue.playNext - Audio ended, playing next...');\n      this.playNext();\n    };\n  }\n\n  /**\n   * Clear the audio queue\n   */\n  clear() {\n    this.queue = [];\n    this.isPlaying = false;\n    this.nextStartTime = 0;\n  }\n\n  /**\n   * Test audio playback with a simple tone\n   */\n  testAudioPlayback() {\n    if (!this.audioContext) {\n      console.error('AudioContext not available for test');\n      return;\n    }\n    console.log('Testing audio playback with 440Hz tone...');\n\n    // Create a 1-second 440Hz sine wave\n    const sampleRate = this.audioContext.sampleRate;\n    const duration = 1.0; // 1 second\n    const frequency = 440; // A4 note\n    const length = sampleRate * duration;\n    const audioBuffer = this.audioContext.createBuffer(1, length, sampleRate);\n    const channelData = audioBuffer.getChannelData(0);\n    for (let i = 0; i < length; i++) {\n      channelData[i] = Math.sin(2 * Math.PI * frequency * i / sampleRate) * 0.3;\n    }\n    const source = this.audioContext.createBufferSource();\n    source.buffer = audioBuffer;\n    source.connect(this.audioContext.destination);\n    source.start();\n    console.log('Test tone started - should hear 440Hz for 1 second');\n  }\n\n  /**\n   * Test µ-law decoding with a simple pattern\n   */\n  testMuLawDecoding() {\n    // Create a simple test pattern\n    const testData = new Uint8Array([0x00, 0x80, 0xFF, 0x7F, 0x40, 0xC0]);\n    const decoded = this.muLawToPcm(testData);\n    console.log('µ-law test - input:', Array.from(testData), 'decoded:', Array.from(decoded));\n    return decoded;\n  }\n}\n\n// Export singleton instance\nexport const audioProcessor = new AudioProcessor();\n\n// Expose for debugging in browser console\nif (typeof window !== 'undefined') {\n  window.audioProcessor = audioProcessor;\n  window.AudioQueue = AudioQueue;\n}", "map": {"version": 3, "names": ["muLawDecodeTable", "Int16Array", "AudioProcessor", "constructor", "sampleRate", "chunkDuration", "chunkSize", "audioBuffer", "pcmToMuLaw", "pcmData", "muLawData", "Uint8Array", "length", "i", "sample", "Math", "max", "min", "round", "sign", "abs", "MULAW_CLIP", "MULAW_BIAS", "exponent", "exp", "mantissa", "muLawToPcm", "Float32Array", "resample", "inputData", "inputSampleRate", "outputSampleRate", "ratio", "outputLength", "floor", "outputData", "sourceIndex", "index", "fraction", "processInputAudio", "processedData", "push", "chunks", "chunk", "splice", "chunkArray", "muLawChunk", "processReceivedAudio", "base64Data", "audioContext", "console", "log", "binaryString", "atob", "charCodeAt", "slice", "maxValue", "minValue", "rms", "sqrt", "reduce", "sum", "val", "warn", "targetSampleRate", "resampledData", "error", "createBuffer", "getChannelData", "set", "duration", "encodeToBase64", "String", "fromCharCode", "btoa", "calculateAudioLevel", "audioData", "applyNoiseGate", "threshold", "<PERSON><PERSON><PERSON><PERSON>", "getBufferSize", "AudioQueue", "queue", "isPlaying", "nextStartTime", "enqueue", "playNext", "shift", "state", "source", "createBufferSource", "buffer", "gainNode", "createGain", "gain", "value", "connect", "destination", "currentTime", "startTime", "numberOfChannels", "channelData", "maxAmplitude", "minAmplitude", "resume", "then", "start", "onended", "clear", "testAudioPlayback", "frequency", "sin", "PI", "testMuLawDecoding", "testData", "decoded", "Array", "from", "audioProcessor", "window"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/utils/audioProcessor.js"], "sourcesContent": ["/**\n * Audio Processing Utilities for WebSocket Voice Protocol\n * Handles µ-law encoding/decoding, resampling, and audio format conversions\n */\n\n// µ-law decoding table that matches <PERSON>'s audioop.ulaw2lin()\n// This is a pre-computed lookup table for faster decoding\nconst muLawDecodeTable = new Int16Array([\n  -32124, -31100, -30076, -29052, -28028, -27004, -25980, -24956,\n  -23932, -22908, -21884, -20860, -19836, -18812, -17788, -16764,\n  -15996, -15484, -14972, -14460, -13948, -13436, -12924, -12412,\n  -11900, -11388, -10876, -10364, -9852, -9340, -8828, -8316,\n  -7932, -7676, -7420, -7164, -6908, -6652, -6396, -6140,\n  -5884, -5628, -5372, -5116, -4860, -4604, -4348, -4092,\n  -3900, -3772, -3644, -3516, -3388, -3260, -3132, -3004,\n  -2876, -2748, -2620, -2492, -2364, -2236, -2108, -1980,\n  -1884, -1820, -1756, -1692, -1628, -1564, -1500, -1436,\n  -1372, -1308, -1244, -1180, -1116, -1052, -988, -924,\n  -876, -844, -812, -780, -748, -716, -684, -652,\n  -620, -588, -556, -524, -492, -460, -428, -396,\n  -372, -356, -340, -324, -308, -292, -276, -260,\n  -244, -228, -212, -196, -180, -164, -148, -132,\n  -120, -112, -104, -96, -88, -80, -72, -64,\n  -56, -48, -40, -32, -24, -16, -8, 0,\n  32124, 31100, 30076, 29052, 28028, 27004, 25980, 24956,\n  23932, 22908, 21884, 20860, 19836, 18812, 17788, 16764,\n  15996, 15484, 14972, 14460, 13948, 13436, 12924, 12412,\n  11900, 11388, 10876, 10364, 9852, 9340, 8828, 8316,\n  7932, 7676, 7420, 7164, 6908, 6652, 6396, 6140,\n  5884, 5628, 5372, 5116, 4860, 4604, 4348, 4092,\n  3900, 3772, 3644, 3516, 3388, 3260, 3132, 3004,\n  2876, 2748, 2620, 2492, 2364, 2236, 2108, 1980,\n  1884, 1820, 1756, 1692, 1628, 1564, 1500, 1436,\n  1372, 1308, 1244, 1180, 1116, 1052, 988, 924,\n  876, 844, 812, 780, 748, 716, 684, 652,\n  620, 588, 556, 524, 492, 460, 428, 396,\n  372, 356, 340, 324, 308, 292, 276, 260,\n  244, 228, 212, 196, 180, 164, 148, 132,\n  120, 112, 104, 96, 88, 80, 72, 64,\n  56, 48, 40, 32, 24, 16, 8, 0\n]);\n\n/**\n * Audio Processor Class\n */\nexport class AudioProcessor {\n  constructor() {\n    this.sampleRate = 8000; // Protocol requirement\n    this.chunkDuration = 20; // 20ms chunks\n    this.chunkSize = (this.sampleRate * this.chunkDuration) / 1000; // 160 samples\n    this.audioBuffer = [];\n  }\n\n  /**\n   * Convert PCM Float32Array to µ-law bytes\n   * @param {Float32Array} pcmData - Input PCM data (-1.0 to 1.0)\n   * @returns {Uint8Array} µ-law encoded data\n   */\n  pcmToMuLaw(pcmData) {\n    const muLawData = new Uint8Array(pcmData.length);\n    \n    for (let i = 0; i < pcmData.length; i++) {\n      // Clamp and scale to 16-bit range\n      let sample = Math.max(-1, Math.min(1, pcmData[i]));\n      sample = Math.round(sample * 32767);\n      \n      // Apply µ-law encoding\n      const sign = sample < 0 ? 0x80 : 0x00;\n      sample = Math.abs(sample);\n      \n      if (sample > MULAW_CLIP) sample = MULAW_CLIP;\n      sample += MULAW_BIAS;\n      \n      let exponent = 7;\n      for (let exp = 0; exp < 8; exp++) {\n        if (sample <= (0x1F << (exp + 3))) {\n          exponent = exp;\n          break;\n        }\n      }\n      \n      const mantissa = (sample >> (exponent + 3)) & 0x0F;\n      muLawData[i] = ~(sign | (exponent << 4) | mantissa);\n    }\n    \n    return muLawData;\n  }\n\n  /**\n   * Convert µ-law bytes to PCM Float32Array\n   * Uses lookup table that matches Python's audioop.ulaw2lin()\n   * @param {Uint8Array} muLawData - Input µ-law data\n   * @returns {Float32Array} PCM data (-1.0 to 1.0)\n   */\n  muLawToPcm(muLawData) {\n    const pcmData = new Float32Array(muLawData.length);\n\n    for (let i = 0; i < muLawData.length; i++) {\n      // Use lookup table for exact compatibility with Python audioop\n      const sample = muLawDecodeTable[muLawData[i]];\n\n      // Normalize to -1.0 to 1.0 range for Web Audio API\n      pcmData[i] = sample / 32768.0;\n    }\n\n    return pcmData;\n  }\n\n  /**\n   * Resample audio data\n   * @param {Float32Array} inputData - Input audio data\n   * @param {number} inputSampleRate - Input sample rate\n   * @param {number} outputSampleRate - Output sample rate\n   * @returns {Float32Array} Resampled audio data\n   */\n  resample(inputData, inputSampleRate, outputSampleRate) {\n    if (inputSampleRate === outputSampleRate) {\n      return inputData;\n    }\n\n    const ratio = inputSampleRate / outputSampleRate;\n    const outputLength = Math.floor(inputData.length / ratio);\n    const outputData = new Float32Array(outputLength);\n\n    for (let i = 0; i < outputLength; i++) {\n      const sourceIndex = i * ratio;\n      const index = Math.floor(sourceIndex);\n      const fraction = sourceIndex - index;\n\n      if (index + 1 < inputData.length) {\n        // Linear interpolation\n        outputData[i] = inputData[index] * (1 - fraction) + inputData[index + 1] * fraction;\n      } else {\n        outputData[i] = inputData[index];\n      }\n    }\n\n    return outputData;\n  }\n\n  /**\n   * Process audio input for transmission\n   * @param {Float32Array} inputData - Raw audio input\n   * @param {number} inputSampleRate - Input sample rate\n   * @returns {Array<Uint8Array>} Array of µ-law encoded chunks\n   */\n  processInputAudio(inputData, inputSampleRate = 44100) {\n    // Resample to 8kHz if needed\n    let processedData = inputData;\n    if (inputSampleRate !== this.sampleRate) {\n      processedData = this.resample(inputData, inputSampleRate, this.sampleRate);\n    }\n\n    // Add to buffer\n    this.audioBuffer.push(...processedData);\n\n    // Extract chunks\n    const chunks = [];\n    while (this.audioBuffer.length >= this.chunkSize) {\n      const chunk = this.audioBuffer.splice(0, this.chunkSize);\n      const chunkArray = new Float32Array(chunk);\n      const muLawChunk = this.pcmToMuLaw(chunkArray);\n      chunks.push(muLawChunk);\n    }\n\n    return chunks;\n  }\n\n  /**\n   * Process received audio for playback\n   * @param {string} base64Data - Base64 encoded µ-law data\n   * @returns {AudioBuffer} Web Audio API AudioBuffer\n   */\n  async processReceivedAudio(base64Data, audioContext) {\n    try {\n      console.log('audioProcessor.processReceivedAudio - Input base64 length:', base64Data.length);\n      \n      // Decode base64 to µ-law data\n      const binaryString = atob(base64Data);\n      console.log('Decoded binary string length:', binaryString.length);\n      \n      const muLawData = new Uint8Array(binaryString.length);\n      for (let i = 0; i < binaryString.length; i++) {\n        muLawData[i] = binaryString.charCodeAt(i);\n      }\n      console.log('µ-law data created, length:', muLawData.length);\n\n      // Convert µ-law to PCM\n      const pcmData = this.muLawToPcm(muLawData);\n      console.log('PCM data converted, length:', pcmData.length, 'sample values:', pcmData.slice(0, 5));\n\n      // Check for valid audio data\n      const maxValue = Math.max(...pcmData);\n      const minValue = Math.min(...pcmData);\n      const rms = Math.sqrt(pcmData.reduce((sum, val) => sum + val * val, 0) / pcmData.length);\n      console.log('PCM data range - max:', maxValue, 'min:', minValue, 'RMS:', rms);\n\n      // Ensure we have valid audio data\n      if (maxValue === 0 && minValue === 0) {\n        console.warn('Audio data appears to be silent (all zeros)');\n      }\n\n      // Create AudioBuffer using AudioContext sample rate, not protocol sample rate\n      console.log('Creating AudioBuffer with AudioContext sample rate:', audioContext.sampleRate, 'vs protocol rate:', this.sampleRate);\n      console.log('PCM data length:', pcmData.length, 'original duration:', pcmData.length / this.sampleRate, 'seconds');\n\n      // Resample PCM data from 8kHz to AudioContext sample rate\n      const targetSampleRate = audioContext.sampleRate;\n      const resampledData = this.resample(pcmData, this.sampleRate, targetSampleRate);\n\n      // Ensure we have a valid length for the audio buffer\n      if (resampledData.length === 0) {\n        console.error('Resampled data is empty');\n        return null;\n      }\n\n      const audioBuffer = audioContext.createBuffer(1, resampledData.length, targetSampleRate);\n      audioBuffer.getChannelData(0).set(resampledData);\n      console.log('AudioBuffer created with resampling - new length:', resampledData.length, 'duration:', audioBuffer.duration, 'seconds');\n\n      return audioBuffer;\n    } catch (error) {\n      console.error('Error processing received audio:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Convert audio data to base64 for transmission\n   * @param {Uint8Array} muLawData - µ-law encoded data\n   * @returns {string} Base64 encoded string\n   */\n  encodeToBase64(muLawData) {\n    let binaryString = '';\n    for (let i = 0; i < muLawData.length; i++) {\n      binaryString += String.fromCharCode(muLawData[i]);\n    }\n    return btoa(binaryString);\n  }\n\n  /**\n   * Calculate audio level (RMS) for visualization\n   * @param {Float32Array} audioData - Audio data\n   * @returns {number} Audio level (0-100)\n   */\n  calculateAudioLevel(audioData) {\n    let sum = 0;\n    for (let i = 0; i < audioData.length; i++) {\n      sum += audioData[i] * audioData[i];\n    }\n    const rms = Math.sqrt(sum / audioData.length);\n    return Math.min(100, rms * 100);\n  }\n\n  /**\n   * Apply noise gate to reduce background noise\n   * @param {Float32Array} audioData - Input audio data\n   * @param {number} threshold - Noise gate threshold (0-1)\n   * @returns {Float32Array} Processed audio data\n   */\n  applyNoiseGate(audioData, threshold = 0.01) {\n    const processedData = new Float32Array(audioData.length);\n    \n    for (let i = 0; i < audioData.length; i++) {\n      const sample = Math.abs(audioData[i]);\n      if (sample > threshold) {\n        processedData[i] = audioData[i];\n      } else {\n        processedData[i] = 0;\n      }\n    }\n    \n    return processedData;\n  }\n\n  /**\n   * Clear internal audio buffer\n   */\n  clearBuffer() {\n    this.audioBuffer = [];\n  }\n\n  /**\n   * Get buffer size\n   * @returns {number} Current buffer size\n   */\n  getBufferSize() {\n    return this.audioBuffer.length;\n  }\n}\n\n/**\n * Audio Queue for managing playback timing\n */\nexport class AudioQueue {\n  constructor(audioContext) {\n    this.audioContext = audioContext;\n    this.queue = [];\n    this.isPlaying = false;\n    this.nextStartTime = 0;\n  }\n\n  /**\n   * Add audio buffer to queue\n   * @param {AudioBuffer} audioBuffer - Audio buffer to queue\n   */\n  enqueue(audioBuffer) {\n    console.log('AudioQueue.enqueue - Adding buffer with duration:', audioBuffer.duration, 'seconds');\n    this.queue.push(audioBuffer);\n    console.log('Queue length now:', this.queue.length, 'isPlaying:', this.isPlaying);\n    if (!this.isPlaying) {\n      this.playNext();\n    }\n  }\n\n  /**\n   * Play next audio buffer in queue\n   */\n  playNext() {\n    if (this.queue.length === 0) {\n      console.log('AudioQueue.playNext - Queue empty, stopping playback');\n      this.isPlaying = false;\n      return;\n    }\n\n    console.log('AudioQueue.playNext - Playing next buffer, queue length:', this.queue.length);\n    this.isPlaying = true;\n    const audioBuffer = this.queue.shift();\n    \n    // Verify AudioContext state\n    console.log('AudioQueue.playNext - AudioContext state:', this.audioContext.state, 'Sample rate:', this.audioContext.sampleRate);\n    \n    const source = this.audioContext.createBufferSource();\n    source.buffer = audioBuffer;\n\n    // Create a gain node for better control\n    const gainNode = this.audioContext.createGain();\n    gainNode.gain.value = 1.0; // Full volume\n\n    // Connect: source -> gain -> destination\n    source.connect(gainNode);\n    gainNode.connect(this.audioContext.destination);\n\n    const currentTime = this.audioContext.currentTime;\n    const startTime = Math.max(currentTime, this.nextStartTime);\n\n    console.log('AudioQueue.playNext - Starting playback at time:', startTime, 'duration:', audioBuffer.duration);\n    console.log('AudioQueue.playNext - Buffer details: channels:', audioBuffer.numberOfChannels, 'length:', audioBuffer.length, 'sampleRate:', audioBuffer.sampleRate);\n\n    // Check if audio data has content\n    const channelData = audioBuffer.getChannelData(0);\n    const maxAmplitude = Math.max(...channelData.slice(0, Math.min(1000, channelData.length)));\n    const minAmplitude = Math.min(...channelData.slice(0, Math.min(1000, channelData.length)));\n    console.log('AudioQueue.playNext - Audio amplitude check - max:', maxAmplitude, 'min:', minAmplitude, 'samples preview:', channelData.slice(0, 5));\n\n    // Ensure AudioContext is running\n    if (this.audioContext.state !== 'running') {\n      console.warn('AudioContext is not running, attempting to resume...');\n      this.audioContext.resume().then(() => {\n        console.log('AudioContext resumed successfully');\n      });\n    }\n\n    source.start(startTime);\n    this.nextStartTime = startTime + audioBuffer.duration;\n\n    source.onended = () => {\n      console.log('AudioQueue.playNext - Audio ended, playing next...');\n      this.playNext();\n    };\n  }\n\n  /**\n   * Clear the audio queue\n   */\n  clear() {\n    this.queue = [];\n    this.isPlaying = false;\n    this.nextStartTime = 0;\n  }\n\n  /**\n   * Test audio playback with a simple tone\n   */\n  testAudioPlayback() {\n    if (!this.audioContext) {\n      console.error('AudioContext not available for test');\n      return;\n    }\n\n    console.log('Testing audio playback with 440Hz tone...');\n\n    // Create a 1-second 440Hz sine wave\n    const sampleRate = this.audioContext.sampleRate;\n    const duration = 1.0; // 1 second\n    const frequency = 440; // A4 note\n    const length = sampleRate * duration;\n\n    const audioBuffer = this.audioContext.createBuffer(1, length, sampleRate);\n    const channelData = audioBuffer.getChannelData(0);\n\n    for (let i = 0; i < length; i++) {\n      channelData[i] = Math.sin(2 * Math.PI * frequency * i / sampleRate) * 0.3;\n    }\n\n    const source = this.audioContext.createBufferSource();\n    source.buffer = audioBuffer;\n    source.connect(this.audioContext.destination);\n    source.start();\n\n    console.log('Test tone started - should hear 440Hz for 1 second');\n  }\n\n  /**\n   * Test µ-law decoding with a simple pattern\n   */\n  testMuLawDecoding() {\n    // Create a simple test pattern\n    const testData = new Uint8Array([0x00, 0x80, 0xFF, 0x7F, 0x40, 0xC0]);\n    const decoded = this.muLawToPcm(testData);\n    console.log('µ-law test - input:', Array.from(testData), 'decoded:', Array.from(decoded));\n    return decoded;\n  }\n}\n\n// Export singleton instance\nexport const audioProcessor = new AudioProcessor();\n\n// Expose for debugging in browser console\nif (typeof window !== 'undefined') {\n  window.audioProcessor = audioProcessor;\n  window.AudioQueue = AudioQueue;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA,MAAMA,gBAAgB,GAAG,IAAIC,UAAU,CAAC,CACtC,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAC9D,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAC9D,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAC9D,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAC1D,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EACtD,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EACtD,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EACtD,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EACtD,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EACtD,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EACpD,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAC9C,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAC9C,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAC9C,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAC9C,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EACzC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EACnC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACtD,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACtD,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACtD,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAClD,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAC9C,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAC9C,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAC9C,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAC9C,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAC9C,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAC5C,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACtC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACtC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACtC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACtC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACjC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAC7B,CAAC;;AAEF;AACA;AACA;AACA,OAAO,MAAMC,cAAc,CAAC;EAC1BC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,UAAU,GAAG,IAAI,CAAC,CAAC;IACxB,IAAI,CAACC,aAAa,GAAG,EAAE,CAAC,CAAC;IACzB,IAAI,CAACC,SAAS,GAAI,IAAI,CAACF,UAAU,GAAG,IAAI,CAACC,aAAa,GAAI,IAAI,CAAC,CAAC;IAChE,IAAI,CAACE,WAAW,GAAG,EAAE;EACvB;;EAEA;AACF;AACA;AACA;AACA;EACEC,UAAUA,CAACC,OAAO,EAAE;IAClB,MAAMC,SAAS,GAAG,IAAIC,UAAU,CAACF,OAAO,CAACG,MAAM,CAAC;IAEhD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,OAAO,CAACG,MAAM,EAAEC,CAAC,EAAE,EAAE;MACvC;MACA,IAAIC,MAAM,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAER,OAAO,CAACI,CAAC,CAAC,CAAC,CAAC;MAClDC,MAAM,GAAGC,IAAI,CAACG,KAAK,CAACJ,MAAM,GAAG,KAAK,CAAC;;MAEnC;MACA,MAAMK,IAAI,GAAGL,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;MACrCA,MAAM,GAAGC,IAAI,CAACK,GAAG,CAACN,MAAM,CAAC;MAEzB,IAAIA,MAAM,GAAGO,UAAU,EAAEP,MAAM,GAAGO,UAAU;MAC5CP,MAAM,IAAIQ,UAAU;MAEpB,IAAIC,QAAQ,GAAG,CAAC;MAChB,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC,EAAEA,GAAG,EAAE,EAAE;QAChC,IAAIV,MAAM,IAAK,IAAI,IAAKU,GAAG,GAAG,CAAG,EAAE;UACjCD,QAAQ,GAAGC,GAAG;UACd;QACF;MACF;MAEA,MAAMC,QAAQ,GAAIX,MAAM,IAAKS,QAAQ,GAAG,CAAE,GAAI,IAAI;MAClDb,SAAS,CAACG,CAAC,CAAC,GAAG,EAAEM,IAAI,GAAII,QAAQ,IAAI,CAAE,GAAGE,QAAQ,CAAC;IACrD;IAEA,OAAOf,SAAS;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEgB,UAAUA,CAAChB,SAAS,EAAE;IACpB,MAAMD,OAAO,GAAG,IAAIkB,YAAY,CAACjB,SAAS,CAACE,MAAM,CAAC;IAElD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;MACzC;MACA,MAAMC,MAAM,GAAGd,gBAAgB,CAACU,SAAS,CAACG,CAAC,CAAC,CAAC;;MAE7C;MACAJ,OAAO,CAACI,CAAC,CAAC,GAAGC,MAAM,GAAG,OAAO;IAC/B;IAEA,OAAOL,OAAO;EAChB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEmB,QAAQA,CAACC,SAAS,EAAEC,eAAe,EAAEC,gBAAgB,EAAE;IACrD,IAAID,eAAe,KAAKC,gBAAgB,EAAE;MACxC,OAAOF,SAAS;IAClB;IAEA,MAAMG,KAAK,GAAGF,eAAe,GAAGC,gBAAgB;IAChD,MAAME,YAAY,GAAGlB,IAAI,CAACmB,KAAK,CAACL,SAAS,CAACjB,MAAM,GAAGoB,KAAK,CAAC;IACzD,MAAMG,UAAU,GAAG,IAAIR,YAAY,CAACM,YAAY,CAAC;IAEjD,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoB,YAAY,EAAEpB,CAAC,EAAE,EAAE;MACrC,MAAMuB,WAAW,GAAGvB,CAAC,GAAGmB,KAAK;MAC7B,MAAMK,KAAK,GAAGtB,IAAI,CAACmB,KAAK,CAACE,WAAW,CAAC;MACrC,MAAME,QAAQ,GAAGF,WAAW,GAAGC,KAAK;MAEpC,IAAIA,KAAK,GAAG,CAAC,GAAGR,SAAS,CAACjB,MAAM,EAAE;QAChC;QACAuB,UAAU,CAACtB,CAAC,CAAC,GAAGgB,SAAS,CAACQ,KAAK,CAAC,IAAI,CAAC,GAAGC,QAAQ,CAAC,GAAGT,SAAS,CAACQ,KAAK,GAAG,CAAC,CAAC,GAAGC,QAAQ;MACrF,CAAC,MAAM;QACLH,UAAU,CAACtB,CAAC,CAAC,GAAGgB,SAAS,CAACQ,KAAK,CAAC;MAClC;IACF;IAEA,OAAOF,UAAU;EACnB;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEI,iBAAiBA,CAACV,SAAS,EAAEC,eAAe,GAAG,KAAK,EAAE;IACpD;IACA,IAAIU,aAAa,GAAGX,SAAS;IAC7B,IAAIC,eAAe,KAAK,IAAI,CAAC1B,UAAU,EAAE;MACvCoC,aAAa,GAAG,IAAI,CAACZ,QAAQ,CAACC,SAAS,EAAEC,eAAe,EAAE,IAAI,CAAC1B,UAAU,CAAC;IAC5E;;IAEA;IACA,IAAI,CAACG,WAAW,CAACkC,IAAI,CAAC,GAAGD,aAAa,CAAC;;IAEvC;IACA,MAAME,MAAM,GAAG,EAAE;IACjB,OAAO,IAAI,CAACnC,WAAW,CAACK,MAAM,IAAI,IAAI,CAACN,SAAS,EAAE;MAChD,MAAMqC,KAAK,GAAG,IAAI,CAACpC,WAAW,CAACqC,MAAM,CAAC,CAAC,EAAE,IAAI,CAACtC,SAAS,CAAC;MACxD,MAAMuC,UAAU,GAAG,IAAIlB,YAAY,CAACgB,KAAK,CAAC;MAC1C,MAAMG,UAAU,GAAG,IAAI,CAACtC,UAAU,CAACqC,UAAU,CAAC;MAC9CH,MAAM,CAACD,IAAI,CAACK,UAAU,CAAC;IACzB;IAEA,OAAOJ,MAAM;EACf;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMK,oBAAoBA,CAACC,UAAU,EAAEC,YAAY,EAAE;IACnD,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,4DAA4D,EAAEH,UAAU,CAACpC,MAAM,CAAC;;MAE5F;MACA,MAAMwC,YAAY,GAAGC,IAAI,CAACL,UAAU,CAAC;MACrCE,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEC,YAAY,CAACxC,MAAM,CAAC;MAEjE,MAAMF,SAAS,GAAG,IAAIC,UAAU,CAACyC,YAAY,CAACxC,MAAM,CAAC;MACrD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuC,YAAY,CAACxC,MAAM,EAAEC,CAAC,EAAE,EAAE;QAC5CH,SAAS,CAACG,CAAC,CAAC,GAAGuC,YAAY,CAACE,UAAU,CAACzC,CAAC,CAAC;MAC3C;MACAqC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEzC,SAAS,CAACE,MAAM,CAAC;;MAE5D;MACA,MAAMH,OAAO,GAAG,IAAI,CAACiB,UAAU,CAAChB,SAAS,CAAC;MAC1CwC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE1C,OAAO,CAACG,MAAM,EAAE,gBAAgB,EAAEH,OAAO,CAAC8C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEjG;MACA,MAAMC,QAAQ,GAAGzC,IAAI,CAACC,GAAG,CAAC,GAAGP,OAAO,CAAC;MACrC,MAAMgD,QAAQ,GAAG1C,IAAI,CAACE,GAAG,CAAC,GAAGR,OAAO,CAAC;MACrC,MAAMiD,GAAG,GAAG3C,IAAI,CAAC4C,IAAI,CAAClD,OAAO,CAACmD,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,GAAGA,GAAG,EAAE,CAAC,CAAC,GAAGrD,OAAO,CAACG,MAAM,CAAC;MACxFsC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEK,QAAQ,EAAE,MAAM,EAAEC,QAAQ,EAAE,MAAM,EAAEC,GAAG,CAAC;;MAE7E;MACA,IAAIF,QAAQ,KAAK,CAAC,IAAIC,QAAQ,KAAK,CAAC,EAAE;QACpCP,OAAO,CAACa,IAAI,CAAC,6CAA6C,CAAC;MAC7D;;MAEA;MACAb,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEF,YAAY,CAAC7C,UAAU,EAAE,mBAAmB,EAAE,IAAI,CAACA,UAAU,CAAC;MACjI8C,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE1C,OAAO,CAACG,MAAM,EAAE,oBAAoB,EAAEH,OAAO,CAACG,MAAM,GAAG,IAAI,CAACR,UAAU,EAAE,SAAS,CAAC;;MAElH;MACA,MAAM4D,gBAAgB,GAAGf,YAAY,CAAC7C,UAAU;MAChD,MAAM6D,aAAa,GAAG,IAAI,CAACrC,QAAQ,CAACnB,OAAO,EAAE,IAAI,CAACL,UAAU,EAAE4D,gBAAgB,CAAC;;MAE/E;MACA,IAAIC,aAAa,CAACrD,MAAM,KAAK,CAAC,EAAE;QAC9BsC,OAAO,CAACgB,KAAK,CAAC,yBAAyB,CAAC;QACxC,OAAO,IAAI;MACb;MAEA,MAAM3D,WAAW,GAAG0C,YAAY,CAACkB,YAAY,CAAC,CAAC,EAAEF,aAAa,CAACrD,MAAM,EAAEoD,gBAAgB,CAAC;MACxFzD,WAAW,CAAC6D,cAAc,CAAC,CAAC,CAAC,CAACC,GAAG,CAACJ,aAAa,CAAC;MAChDf,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAEc,aAAa,CAACrD,MAAM,EAAE,WAAW,EAAEL,WAAW,CAAC+D,QAAQ,EAAE,SAAS,CAAC;MAEpI,OAAO/D,WAAW;IACpB,CAAC,CAAC,OAAO2D,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACEK,cAAcA,CAAC7D,SAAS,EAAE;IACxB,IAAI0C,YAAY,GAAG,EAAE;IACrB,KAAK,IAAIvC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;MACzCuC,YAAY,IAAIoB,MAAM,CAACC,YAAY,CAAC/D,SAAS,CAACG,CAAC,CAAC,CAAC;IACnD;IACA,OAAO6D,IAAI,CAACtB,YAAY,CAAC;EAC3B;;EAEA;AACF;AACA;AACA;AACA;EACEuB,mBAAmBA,CAACC,SAAS,EAAE;IAC7B,IAAIf,GAAG,GAAG,CAAC;IACX,KAAK,IAAIhD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+D,SAAS,CAAChE,MAAM,EAAEC,CAAC,EAAE,EAAE;MACzCgD,GAAG,IAAIe,SAAS,CAAC/D,CAAC,CAAC,GAAG+D,SAAS,CAAC/D,CAAC,CAAC;IACpC;IACA,MAAM6C,GAAG,GAAG3C,IAAI,CAAC4C,IAAI,CAACE,GAAG,GAAGe,SAAS,CAAChE,MAAM,CAAC;IAC7C,OAAOG,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEyC,GAAG,GAAG,GAAG,CAAC;EACjC;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEmB,cAAcA,CAACD,SAAS,EAAEE,SAAS,GAAG,IAAI,EAAE;IAC1C,MAAMtC,aAAa,GAAG,IAAIb,YAAY,CAACiD,SAAS,CAAChE,MAAM,CAAC;IAExD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+D,SAAS,CAAChE,MAAM,EAAEC,CAAC,EAAE,EAAE;MACzC,MAAMC,MAAM,GAAGC,IAAI,CAACK,GAAG,CAACwD,SAAS,CAAC/D,CAAC,CAAC,CAAC;MACrC,IAAIC,MAAM,GAAGgE,SAAS,EAAE;QACtBtC,aAAa,CAAC3B,CAAC,CAAC,GAAG+D,SAAS,CAAC/D,CAAC,CAAC;MACjC,CAAC,MAAM;QACL2B,aAAa,CAAC3B,CAAC,CAAC,GAAG,CAAC;MACtB;IACF;IAEA,OAAO2B,aAAa;EACtB;;EAEA;AACF;AACA;EACEuC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACxE,WAAW,GAAG,EAAE;EACvB;;EAEA;AACF;AACA;AACA;EACEyE,aAAaA,CAAA,EAAG;IACd,OAAO,IAAI,CAACzE,WAAW,CAACK,MAAM;EAChC;AACF;;AAEA;AACA;AACA;AACA,OAAO,MAAMqE,UAAU,CAAC;EACtB9E,WAAWA,CAAC8C,YAAY,EAAE;IACxB,IAAI,CAACA,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACiC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,aAAa,GAAG,CAAC;EACxB;;EAEA;AACF;AACA;AACA;EACEC,OAAOA,CAAC9E,WAAW,EAAE;IACnB2C,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE5C,WAAW,CAAC+D,QAAQ,EAAE,SAAS,CAAC;IACjG,IAAI,CAACY,KAAK,CAACzC,IAAI,CAAClC,WAAW,CAAC;IAC5B2C,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC+B,KAAK,CAACtE,MAAM,EAAE,YAAY,EAAE,IAAI,CAACuE,SAAS,CAAC;IACjF,IAAI,CAAC,IAAI,CAACA,SAAS,EAAE;MACnB,IAAI,CAACG,QAAQ,CAAC,CAAC;IACjB;EACF;;EAEA;AACF;AACA;EACEA,QAAQA,CAAA,EAAG;IACT,IAAI,IAAI,CAACJ,KAAK,CAACtE,MAAM,KAAK,CAAC,EAAE;MAC3BsC,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;MACnE,IAAI,CAACgC,SAAS,GAAG,KAAK;MACtB;IACF;IAEAjC,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAE,IAAI,CAAC+B,KAAK,CAACtE,MAAM,CAAC;IAC1F,IAAI,CAACuE,SAAS,GAAG,IAAI;IACrB,MAAM5E,WAAW,GAAG,IAAI,CAAC2E,KAAK,CAACK,KAAK,CAAC,CAAC;;IAEtC;IACArC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE,IAAI,CAACF,YAAY,CAACuC,KAAK,EAAE,cAAc,EAAE,IAAI,CAACvC,YAAY,CAAC7C,UAAU,CAAC;IAE/H,MAAMqF,MAAM,GAAG,IAAI,CAACxC,YAAY,CAACyC,kBAAkB,CAAC,CAAC;IACrDD,MAAM,CAACE,MAAM,GAAGpF,WAAW;;IAE3B;IACA,MAAMqF,QAAQ,GAAG,IAAI,CAAC3C,YAAY,CAAC4C,UAAU,CAAC,CAAC;IAC/CD,QAAQ,CAACE,IAAI,CAACC,KAAK,GAAG,GAAG,CAAC,CAAC;;IAE3B;IACAN,MAAM,CAACO,OAAO,CAACJ,QAAQ,CAAC;IACxBA,QAAQ,CAACI,OAAO,CAAC,IAAI,CAAC/C,YAAY,CAACgD,WAAW,CAAC;IAE/C,MAAMC,WAAW,GAAG,IAAI,CAACjD,YAAY,CAACiD,WAAW;IACjD,MAAMC,SAAS,GAAGpF,IAAI,CAACC,GAAG,CAACkF,WAAW,EAAE,IAAI,CAACd,aAAa,CAAC;IAE3DlC,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEgD,SAAS,EAAE,WAAW,EAAE5F,WAAW,CAAC+D,QAAQ,CAAC;IAC7GpB,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAE5C,WAAW,CAAC6F,gBAAgB,EAAE,SAAS,EAAE7F,WAAW,CAACK,MAAM,EAAE,aAAa,EAAEL,WAAW,CAACH,UAAU,CAAC;;IAElK;IACA,MAAMiG,WAAW,GAAG9F,WAAW,CAAC6D,cAAc,CAAC,CAAC,CAAC;IACjD,MAAMkC,YAAY,GAAGvF,IAAI,CAACC,GAAG,CAAC,GAAGqF,WAAW,CAAC9C,KAAK,CAAC,CAAC,EAAExC,IAAI,CAACE,GAAG,CAAC,IAAI,EAAEoF,WAAW,CAACzF,MAAM,CAAC,CAAC,CAAC;IAC1F,MAAM2F,YAAY,GAAGxF,IAAI,CAACE,GAAG,CAAC,GAAGoF,WAAW,CAAC9C,KAAK,CAAC,CAAC,EAAExC,IAAI,CAACE,GAAG,CAAC,IAAI,EAAEoF,WAAW,CAACzF,MAAM,CAAC,CAAC,CAAC;IAC1FsC,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAEmD,YAAY,EAAE,MAAM,EAAEC,YAAY,EAAE,kBAAkB,EAAEF,WAAW,CAAC9C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;IAElJ;IACA,IAAI,IAAI,CAACN,YAAY,CAACuC,KAAK,KAAK,SAAS,EAAE;MACzCtC,OAAO,CAACa,IAAI,CAAC,sDAAsD,CAAC;MACpE,IAAI,CAACd,YAAY,CAACuD,MAAM,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACpCvD,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAClD,CAAC,CAAC;IACJ;IAEAsC,MAAM,CAACiB,KAAK,CAACP,SAAS,CAAC;IACvB,IAAI,CAACf,aAAa,GAAGe,SAAS,GAAG5F,WAAW,CAAC+D,QAAQ;IAErDmB,MAAM,CAACkB,OAAO,GAAG,MAAM;MACrBzD,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MACjE,IAAI,CAACmC,QAAQ,CAAC,CAAC;IACjB,CAAC;EACH;;EAEA;AACF;AACA;EACEsB,KAAKA,CAAA,EAAG;IACN,IAAI,CAAC1B,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,aAAa,GAAG,CAAC;EACxB;;EAEA;AACF;AACA;EACEyB,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAAC,IAAI,CAAC5D,YAAY,EAAE;MACtBC,OAAO,CAACgB,KAAK,CAAC,qCAAqC,CAAC;MACpD;IACF;IAEAhB,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;;IAExD;IACA,MAAM/C,UAAU,GAAG,IAAI,CAAC6C,YAAY,CAAC7C,UAAU;IAC/C,MAAMkE,QAAQ,GAAG,GAAG,CAAC,CAAC;IACtB,MAAMwC,SAAS,GAAG,GAAG,CAAC,CAAC;IACvB,MAAMlG,MAAM,GAAGR,UAAU,GAAGkE,QAAQ;IAEpC,MAAM/D,WAAW,GAAG,IAAI,CAAC0C,YAAY,CAACkB,YAAY,CAAC,CAAC,EAAEvD,MAAM,EAAER,UAAU,CAAC;IACzE,MAAMiG,WAAW,GAAG9F,WAAW,CAAC6D,cAAc,CAAC,CAAC,CAAC;IAEjD,KAAK,IAAIvD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,EAAE;MAC/BwF,WAAW,CAACxF,CAAC,CAAC,GAAGE,IAAI,CAACgG,GAAG,CAAC,CAAC,GAAGhG,IAAI,CAACiG,EAAE,GAAGF,SAAS,GAAGjG,CAAC,GAAGT,UAAU,CAAC,GAAG,GAAG;IAC3E;IAEA,MAAMqF,MAAM,GAAG,IAAI,CAACxC,YAAY,CAACyC,kBAAkB,CAAC,CAAC;IACrDD,MAAM,CAACE,MAAM,GAAGpF,WAAW;IAC3BkF,MAAM,CAACO,OAAO,CAAC,IAAI,CAAC/C,YAAY,CAACgD,WAAW,CAAC;IAC7CR,MAAM,CAACiB,KAAK,CAAC,CAAC;IAEdxD,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;EACnE;;EAEA;AACF;AACA;EACE8D,iBAAiBA,CAAA,EAAG;IAClB;IACA,MAAMC,QAAQ,GAAG,IAAIvG,UAAU,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACrE,MAAMwG,OAAO,GAAG,IAAI,CAACzF,UAAU,CAACwF,QAAQ,CAAC;IACzChE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEiE,KAAK,CAACC,IAAI,CAACH,QAAQ,CAAC,EAAE,UAAU,EAAEE,KAAK,CAACC,IAAI,CAACF,OAAO,CAAC,CAAC;IACzF,OAAOA,OAAO;EAChB;AACF;;AAEA;AACA,OAAO,MAAMG,cAAc,GAAG,IAAIpH,cAAc,CAAC,CAAC;;AAElD;AACA,IAAI,OAAOqH,MAAM,KAAK,WAAW,EAAE;EACjCA,MAAM,CAACD,cAAc,GAAGA,cAAc;EACtCC,MAAM,CAACtC,UAAU,GAAGA,UAAU;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}